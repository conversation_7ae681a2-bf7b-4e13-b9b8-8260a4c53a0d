enum TreatmentStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  ON_HOLD
}

enum TreatmentPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model Treatment {
  id              Int               @id @default(autoincrement())
  tenantId        String
  tenant          Tenant            @relation(fields: [tenantId], references: [id])
  
  // Relationships
  findingId       Int
  finding         Finding           @relation(fields: [findingId], references: [id])
  
  // Treatment details
  procedureCode   String
  procedureName   String
  cost            Decimal
  status          TreatmentStatus   @default(PLANNED)
  priority        TreatmentPriority @default(MEDIUM)
  
  // Scheduling
  plannedDate     DateTime?
  completedDate   DateTime?
  
  // Provider assignment
  assignedToId    Int?
  assignedTo      User?             @relation("AssignedTo", fields: [assignedToId], references: [id])
  completedById   Int?
  completedBy     User?             @relation("CompletedBy", fields: [completedById], references: [id])
  
  // Notes
  notes           String?           // Max 1000 characters
  
  // Audit fields
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  createdById     Int?
  updatedById     Int?
  
  @@index([tenantId, findingId, createdAt])
  @@index([tenantId, status])
  @@index([tenantId, priority])
  @@index([tenantId, procedureCode])
  @@index([tenantId, completedDate])
  @@index([tenantId, plannedDate])
  @@index([tenantId, assignedToId])
  @@index([tenantId, completedById])
}