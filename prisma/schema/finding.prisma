enum FindingCategory {
  CARIES
  PERIODONTAL
  ENDODONTIC
  ORAL_PATHOLOGY
  ORTHODONTIC
  ORAL_SURGERY
  PROSTHODONTIC
  PREVENTIVE
  TMJ
  TRAUMA
  CONGENITAL
  OTHER
}

enum FindingSeverity {
  MILD
  MODERATE
  SEVERE
  EXTENSIVE
}

enum FindingPrognosis {
  GOOD
  FAIR
  POOR
  HOPELESS
}

enum FindingStatus {
  ACTIVE
  RESOLVED
  MONITORING
}

model Finding {
  id            Int               @id @default(autoincrement())
  tenantId      String
  tenant        Tenant            @relation(fields: [tenantId], references: [id])
  
  // Relationships
  toothId       Int
  tooth         Tooth             @relation(fields: [toothId], references: [id])
  
  // Finding details
  category      FindingCategory
  subcategory   String
  severity      FindingSeverity   @default(MILD)
  prognosis     FindingPrognosis  @default(GOOD)
  status        FindingStatus     @default(ACTIVE)
  
  // Provider and timing
  recordedById  Int?
  recordedBy    User?             @relation("RecordedBy", fields: [recordedById], references: [id])
  recordedDate  DateTime          @default(now())
  
  // Notes
  notes         String?           // Max 500 characters
  
  // Audit fields
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Relations
  diagnoses     Diagnosis[]
  treatments    Treatment[]
  
  @@index([tenantId, toothId, recordedDate])
  @@index([tenantId, category])
  @@index([tenantId, severity])
  @@index([tenantId, prognosis])
  @@index([tenantId, status])
  @@index([tenantId, recordedById])
}