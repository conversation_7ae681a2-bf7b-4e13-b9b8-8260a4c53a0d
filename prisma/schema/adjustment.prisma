enum AdjustmentType {
  DISCOUNT
  WRITE_OFF
  COURTESY_ADJUSTMENT
  INSURANCE_ADJUSTMENT
  PROMPT_PAY_DISCOUNT
  FAMILY_DISCOUNT
  SENIOR_DISCOUNT
  HARDSHIP_ADJUSTMENT
  BILLING_ERROR
  OTHER
}

enum AdjustmentStatus {
  PENDING
  APPROVED
  DENIED
  APPLIED
}

model Adjustment {
  id                Int              @id @default(autoincrement())
  tenantId          String
  tenant            Tenant           @relation(fields: [tenantId], references: [id])
  
  // Relationships
  patientId         Int
  patient           Patient          @relation(fields: [patientId], references: [id])
  invoiceId         Int?
  invoice           Invoice?         @relation(fields: [invoiceId], references: [id])
  
  // Adjustment details
  adjustmentNumber  String           @unique
  adjustmentDate    DateTime
  adjustmentType    AdjustmentType
  amount            Decimal
  status            AdjustmentStatus @default(PENDING)
  
  // Reason and approval
  reason            String
  notes             String?
  
  // Staff tracking
  approvedById      Int?
  approvedBy        User?            @relation("ApprovedBy", fields: [approvedById], references: [id])
  createdById       Int?
  createdBy         User?            @relation("CreatedBy", fields: [createdById], references: [id])
  
  // Audit fields
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  
  @@unique([tenantId, adjustmentNumber])
  @@index([tenantId, patientId, adjustmentDate])
  @@index([tenantId, adjustmentType])
  @@index([tenantId, status])
  @@index([tenantId, invoiceId])
}