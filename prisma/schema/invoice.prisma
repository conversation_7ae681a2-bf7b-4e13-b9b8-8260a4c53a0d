enum InvoiceStatus {
  DRAFT
  PENDING
  SENT
  VIEWED
  PARTIAL_PAYMENT
  PAID
  OVERDUE
  CANCELLED
  REFUNDED
  WRITE_OFF
}

model Invoice {
  id                  Int                   @id @default(autoincrement())
  tenantId            String
  tenant              Tenant                @relation(fields: [tenantId], references: [id])
  
  // Relationships
  patientId           Int
  patient             Patient               @relation(fields: [patientId], references: [id])
  
  // Invoice details
  invoiceNumber       String                @unique
  invoiceDate         DateTime
  dueDate             DateTime
  status              InvoiceStatus         @default(DRAFT)
  
  // Financial amounts
  subtotal            Decimal               @default(0.00)
  totalAmount         Decimal               @default(0.00)
  amountPaid          Decimal               @default(0.00)
  balanceDue          Decimal               @default(0.00)
  
  // Payment terms
  paymentTermsDays    Int                   @default(30)
  
  // Text-to-pay functionality
  textToPaySent       Boolean               @default(false)
  textToPayLink       String?
  
  // Audit fields
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  createdById         Int?
  updatedById         Int?
  
  // Relations
  payments            Payment[]
  paymentApplications PaymentApplication[]
  adjustments         Adjustment[]
  
  @@unique([tenantId, invoiceNumber])
  @@index([tenantId, patientId, invoiceDate])
  @@index([tenantId, invoiceNumber])
  @@index([tenantId, status])
  @@index([tenantId, dueDate])
}