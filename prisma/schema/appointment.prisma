enum AppointmentType {
  CONSULTATION
  ROUTINE_CHECKUP
  CLEANING
  EMERGENCY
  TREATMENT
  FOLLOW_UP
  SURGERY
  ORTHODONTIC
  RECALL
  NEW_PATIENT
  TELEHEALTH
  BLOCK_TIME
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  CHECKED_IN
  IN_PROGRESS
  COMPLETED
  NO_SHOW
  CANCELLED
  RESCHEDULED
  LATE_CANCELLATION
}

enum AppointmentPriority {
  EMERGENCY
  URGENT
  ROUTINE
  ELECTIVE
}

enum RecallType {
  ROUTINE_CLEANING
  PERIODONTAL_MAINTENANCE
  ORTHODONTIC_ADJUSTMENT
  POST_TREATMENT
  FLUORIDE_TREATMENT
  ORAL_CANCER_SCREENING
  CUSTOM
}

enum ConfirmationMethod {
  EMAIL
  SMS
  PHONE
  WHATSAPP
  PATIENT_PORTAL
}

enum CancelledBy {
  PATIENT
  PROVIDER
  STAFF
  SYSTEM
}

model Appointment {
  id                      Int                 @id @default(autoincrement())
  tenantId                String
  tenant                  Tenant              @relation(fields: [tenantId], references: [id])
  
  // Core appointment data
  appointmentNumber       String              @unique
  patientId               Int
  patient                 Patient             @relation(fields: [patientId], references: [id])
  
  // Schedule details
  appointmentDate         DateTime
  durationMinutes         Int                 @default(60)
  endTime                 DateTime?
  
  // Appointment details
  appointmentType         AppointmentType     @default(CONSULTATION)
  status                  AppointmentStatus   @default(SCHEDULED)
  priority                AppointmentPriority @default(ROUTINE)
  
  // Provider assignment
  primaryProviderId       Int?
  primaryProvider         User?               @relation("PrimaryProvider", fields: [primaryProviderId], references: [id])
  assistingProviders      User[]              @relation("AssistingProviders")
  
  // Clinical information
  chiefComplaint          String?
  procedureCodes          String?
  clinicalNotes           String?
  
  // Recall functionality
  isRecallAppointment     Boolean             @default(false)
  recallType              RecallType?
  recallIntervalMonths    Int?
  
  // Room and resources
  treatmentRoom           String?
  specialEquipmentNeeded  String?
  
  // Patient communication
  confirmationSent        Boolean             @default(false)
  confirmationMethod      ConfirmationMethod?
  confirmationDate        DateTime?
  reminderSent            Boolean             @default(false)
  reminderDate            DateTime?
  
  // Timing and check-in
  checkInTime             DateTime?
  actualStartTime         DateTime?
  actualEndTime           DateTime?
  
  // Financial information
  estimatedFee            Decimal?
  insurancePreAuth        String?
  copayCollected          Decimal?
  
  // Special requirements
  specialInstructions     String?
  preMedicationRequired   Boolean             @default(false)
  preMedicationNotes      String?
  wheelchairAccessible    Boolean             @default(false)
  interpreterNeeded       Boolean             @default(false)
  interpreterLanguage     String?
  
  // Cancellation and rescheduling
  cancellationReason      String?
  cancelledBy             CancelledBy?
  cancellationDate        DateTime?
  
  // No-show handling
  noShowFee               Decimal?
  noShowNotes             String?
  
  // Follow-up
  followUpNeeded          Boolean             @default(false)
  followUpIntervalWeeks   Int?
  
  // Quality and satisfaction
  patientSatisfactionScore Int?               // 1-5 scale
  patientFeedback         String?
  
  // Audit fields
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  createdById             Int?
  updatedById             Int?
  createdBy               User?               @relation("CreatedBy", fields: [createdById], references: [id])
  modifiedBy              User?               @relation("ModifiedBy", fields: [updatedById], references: [id])
  
  // Relations
  rescheduledFromId       Int?
  rescheduledFrom         Appointment?        @relation("RescheduledAppointments", fields: [rescheduledFromId], references: [id])
  rescheduledAppointments Appointment[]       @relation("RescheduledAppointments")
  
  @@unique([tenantId, appointmentNumber])
  @@index([tenantId, patientId, appointmentDate])
  @@index([tenantId, appointmentDate])
  @@index([tenantId, primaryProviderId, appointmentDate])
  @@index([tenantId, status])
  @@index([tenantId, appointmentType])
  @@index([tenantId, isRecallAppointment])
  @@index([tenantId, treatmentRoom, appointmentDate])
}