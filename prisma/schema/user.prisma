
enum UserType {
  ADMIN
  RECEPTIONIST
  PATIENT
  DENTIST
}

model User {
  id                Int       @id @default(autoincrement())
  tenantId          String
  tenant            Tenant    @relation(fields: [tenantId], references: [id])
  
  // Authentication fields
  username          String?   @unique
  phoneNumber       String?   @unique
  email             String?   @unique
  password          String
  
  // User details
  firstName         String?
  lastName          String?
  userType          UserType  @default(PATIENT)
  
  // Django AbstractUser fields
  isStaff           <PERSON>an   @default(false)
  isActive          Boolean   @default(true)
  isSuperuser       <PERSON>   @default(false)
  lastLogin         DateTime?
  lastLoginIp       String?
  dateJoined        DateTime  @default(now())
  
  // Audit fields
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdById       Int?
  updatedById       Int?
  
  // Self-referential relations for audit
  createdBy         User?     @relation("UserCreatedBy", fields: [createdById], references: [id])
  usersCreated      User[]    @relation("UserCreatedBy")
  updatedBy         User?     @relation("UserUpdatedBy", fields: [updatedById], references: [id])
  usersUpdated      User[]    @relation("UserUpdatedBy")
  
  // Relations
  patientProfile           Patient?      @relation("PatientUser")
  primaryAppointments      Appointment[] @relation("PrimaryProvider")
  assistingAppointments    Appointment[] @relation("AssistingProviders")
  createdAppointments      Appointment[] @relation("CreatedBy")
  modifiedAppointments     Appointment[] @relation("ModifiedBy")
  completedTreatments      Treatment[]   @relation("CompletedBy")
  assignedTreatments       Treatment[]   @relation("AssignedTo")
  madeDiagnoses           Diagnosis[]   @relation("DiagnosingProvider")
  recordedFindings        Finding[]     @relation("RecordedBy")
  receivedPayments        Payment[]     @relation("ReceivedBy")
  processedPayments       Payment[]     @relation("ProcessedBy")
  appliedPayments         PaymentApplication[] @relation("AppliedBy")
  approvedAdjustments     Adjustment[]  @relation("ApprovedBy")
  createdAdjustments      Adjustment[]  @relation("CreatedBy")
  
  @@unique([tenantId, username])
  @@unique([tenantId, phoneNumber])
  @@unique([tenantId, email])
  @@index([tenantId, userType])
  @@index([tenantId, isActive])
}

