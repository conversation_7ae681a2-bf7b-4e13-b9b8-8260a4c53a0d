enum DiagnosisCategory {
  CARIES
  PERIODONTAL
  ENDODONTIC
  ORAL_PATHOLOGY
  ORTHODONTIC
  ORAL_SURGERY
  PROSTHODONTIC
  PREVENTIVE
  TMJ
  TRAUMA
  CONGENITAL
  OTHER
}

enum DiagnosisSeverity {
  MILD
  MODERATE
  SEVERE
  EXTENSIVE
}

enum DiagnosisPrognosis {
  GOOD
  FAIR
  POOR
  HOPELESS
}

enum DiagnosisStatus {
  ACTIVE
  STABLE
  RESOLVED
  CHRONIC
  RECURRENT
}

model Diagnosis {
  id                  Int                 @id @default(autoincrement())
  tenantId            String
  tenant              Tenant              @relation(fields: [tenantId], references: [id])
  
  // Relationships
  findingId           Int
  finding             Finding             @relation(fields: [findingId], references: [id])
  
  // Diagnosis details
  diagnosisDate       DateTime            @default(now())
  category            DiagnosisCategory
  severity            DiagnosisSeverity
  prognosis           DiagnosisPrognosis
  status              DiagnosisStatus     @default(ACTIVE)
  
  // Provider
  diagnosingProviderId Int?
  diagnosingProvider  User?               @relation("DiagnosingProvider", fields: [diagnosingProviderId], references: [id])
  
  // Notes
  notes               String?             // Max 500 characters
  
  // Audit fields
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt
  createdById         Int?
  updatedById         Int?
  
  @@index([tenantId, findingId, diagnosisDate])
  @@index([tenantId, category])
  @@index([tenantId, severity])
  @@index([tenantId, prognosis])
  @@index([tenantId, status])
  @@index([tenantId, diagnosingProviderId])
}