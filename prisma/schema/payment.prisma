enum PaymentMethod {
  CASH
  CHECK
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  ACH
  PAYPAL
  VENMO
  APPLE_PAY
  GOOGLE_PAY
  INSURANCE
  CARE_CREDIT
  PAYMENT_PLAN
  GIFT_CARD
  LOYALTY_POINTS
  OTHER
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  DISPUTED
  CHARGEBACK
}

enum PaymentSource {
  FRONT_DESK
  ONLINE
  PHONE
  MAIL
  MOBILE_APP
  TEXT_TO_PAY
  AUTO_PAY
  KIOSK
}

enum CardType {
  VISA
  MASTERCARD
  AMEX
  DISCOVER
  OTHER
}

enum ReceiptMethod {
  EMAIL
  SMS
  PRINT
  NONE
}

model Payment {
  id                Int                   @id @default(autoincrement())
  tenantId          String
  tenant            Tenant                @relation(fields: [tenantId], references: [id])
  
  // Relationships
  patientId         Int
  patient           Patient               @relation(fields: [patientId], references: [id])
  invoiceId         Int?
  invoice           Invoice?              @relation(fields: [invoiceId], references: [id])
  
  // Payment details
  paymentNumber     String                @unique
  paymentDate       DateTime
  amount            Decimal
  appliedAmount     Decimal               @default(0.00)
  unappliedAmount   Decimal               @default(0.00)
  
  // Payment method and processing
  paymentMethod     PaymentMethod
  status            PaymentStatus         @default(COMPLETED)
  paymentSource     PaymentSource         @default(FRONT_DESK)
  
  // Processing details
  transactionId     String?
  processorResponse String?
  
  // Check-specific fields
  checkNumber       String?
  bankName          String?
  
  // Card-specific fields
  cardLastFour      String?
  cardType          CardType?
  
  // Processing fees
  processingFee     Decimal               @default(0.00)
  netAmount         Decimal               @default(0.00)
  
  // Staff tracking
  receivedById      Int?
  receivedBy        User?                 @relation("ReceivedBy", fields: [receivedById], references: [id])
  processedById     Int?
  processedBy       User?                 @relation("ProcessedBy", fields: [processedById], references: [id])
  
  // Reconciliation
  isDeposited       Boolean               @default(false)
  depositDate       DateTime?
  depositBatch      String?
  
  // Refund handling
  isRefunded        Boolean               @default(false)
  refundDate        DateTime?
  refundAmount      Decimal?
  refundReason      String?
  
  // Notes and receipt
  notes             String?
  internalNotes     String?
  receiptSent       Boolean               @default(false)
  receiptEmail      String?
  receiptMethod     ReceiptMethod         @default(EMAIL)
  
  // Audit fields
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  createdById       Int?
  updatedById       Int?
  
  // Relations
  applications      PaymentApplication[]
  
  @@unique([tenantId, paymentNumber])
  @@index([tenantId, patientId, paymentDate])
  @@index([tenantId, paymentNumber])
  @@index([tenantId, status])
  @@index([tenantId, paymentMethod])
  @@index([tenantId, invoiceId])
  @@index([tenantId, transactionId])
}

model PaymentApplication {
  id              Int      @id @default(autoincrement())
  tenantId        String
  
  // Relationships
  paymentId       Int
  payment         Payment  @relation(fields: [paymentId], references: [id])
  invoiceId       Int
  invoice         Invoice  @relation(fields: [invoiceId], references: [id])
  
  // Application details
  amount          Decimal
  applicationDate DateTime @default(now())
  
  // Administrative
  appliedById     Int?
  appliedBy       User?    @relation("AppliedBy", fields: [appliedById], references: [id])
  notes           String?
  
  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdById     Int?
  updatedById     Int?
  
  @@index([tenantId, paymentId, applicationDate])
  @@index([tenantId, invoiceId, applicationDate])
}