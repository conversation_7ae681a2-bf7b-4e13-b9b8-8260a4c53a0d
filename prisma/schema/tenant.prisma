model Tenant {
  id        String   @id @default(uuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  users         User[]
  patients      Patient[]
  appointments  Appointment[]
  caseSheets    CaseSheet[]
  teeth         Tooth[]
  findings      Finding[]
  diagnoses     Diagnosis[]
  treatments    Treatment[]
  invoices      Invoice[]
  payments      Payment[]
  adjustments   Adjustment[]
}