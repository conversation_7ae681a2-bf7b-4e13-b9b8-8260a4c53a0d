enum PatientStatus {
  ACTIVE
  INACTIVE
}

model Patient {
  id            Int           @id @default(autoincrement())
  tenantId      String
  tenant        Tenant        @relation(fields: [tenantId], references: [id])
  
  // User relationship
  userId        Int?          @unique
  user          User?         @relation("PatientUser", fields: [userId], references: [id])
  
  // Core demographics
  clinicId      String        @unique
  firstName     String
  lastName      String
  dateOfBirth   DateTime?
  
  // Contact information
  phoneNumber   String?
  email         String?
  address       String?
  
  // Status
  status        PatientStatus @default(ACTIVE)
  
  // Audit fields
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Relations
  appointments  Appointment[]
  caseSheet     CaseSheet?
  invoices      Invoice[]
  payments      Payment[]
  adjustments   Adjustment[]
  
  @@unique([tenantId, clinicId])
  @@index([tenantId, lastName, firstName])
  @@index([tenantId, phoneNumber])
  @@index([tenantId, email])
  @@index([tenantId, status])
}