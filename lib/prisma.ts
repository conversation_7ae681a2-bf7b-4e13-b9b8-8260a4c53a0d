import { PrismaClient } from '@prisma/client';
import { allExtensions } from './prisma-extensions';

const globalForPrisma = globalThis as unknown as {
  prisma: ReturnType<typeof createPrismaClient> | undefined;
};

function createPrismaClient() {
  const baseClient = new PrismaClient({
    log: ['query'],
  });

  // Apply all extensions to the client
  let extendedClient = baseClient;
  for (const extension of allExtensions) {
    extendedClient = extendedClient.$extends(extension);
  }

  return extendedClient;
}

const client = createPrismaClient();

export const prisma = globalForPrisma.prisma ?? client;

// Export TenantContext and TenantContextManager for tenant management
export { TenantContext } from './prisma-extensions';
export { TenantContextManager } from './tenant-context';

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = client;

export default prisma;