import { patientExtension } from './prisma-extensions/patient.js';

// Test the patient extension functionality
async function testPatientExtension() {
  try {
    console.log('Testing patient extension...');

    // Test that the extension is properly structured
    console.log('Patient extension name:', patientExtension.name);
    console.log('Extension has model methods:', !!patientExtension.model);
    console.log('Extension has result methods:', !!patientExtension.result);

    // Test computed properties with mock data
    const mockPatient = {
      id: 1,
      tenantId: 'test-tenant-id',
      userId: null,
      clinicId: 'P123456',
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      phoneNumber: '**********',
      email: '<EMAIL>',
      address: '123 Main St',
      status: 'ACTIVE' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdById: null,
      updatedById: null,
    };

    // Test computed properties
    console.log('Full name:', mockPatient.firstName + ' ' + mockPatient.lastName);
    
    // Calculate age manually for testing
    const today = new Date();
    const birthDate = new Date(mockPatient.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    console.log('Age:', age);

    console.log('Patient extension test completed successfully!');
  } catch (error) {
    console.error('Error testing patient extension:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testPatientExtension();
}

export { testPatientExtension };