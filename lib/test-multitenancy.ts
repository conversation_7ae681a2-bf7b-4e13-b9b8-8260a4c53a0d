/**
 * Simple test script to verify multitenancy extension functionality
 * Run with: npx tsx lib/test-multitenancy.ts
 */

import { prisma, TenantContext } from './prisma';

async function testMultitenancy() {
  console.log('🧪 Testing Multitenancy Extension...\n');

  try {
    // Test 1: Verify TenantContext works
    console.log('Test 1: TenantContext management');
    console.log('Initial tenant ID:', TenantContext.getCurrentTenantId());
    
    TenantContext.setCurrentTenantId('tenant-123');
    console.log('After setting tenant-123:', TenantContext.getCurrentTenantId());
    
    TenantContext.clearTenantId();
    console.log('After clearing:', TenantContext.getCurrentTenantId());
    console.log('✅ TenantContext works correctly\n');

    // Test 2: Verify client has multitenancy methods
    console.log('Test 2: Client extension methods');
    console.log('Has getCurrentTenantId method:', typeof prisma.getCurrentTenantId === 'function');
    console.log('Has bypassTenant method:', typeof prisma.bypassTenant === 'function');
    console.log('✅ Client extension methods available\n');

    // Test 3: Test getCurrentTenantId method
    console.log('Test 3: Client getCurrentTenantId method');
    TenantContext.setCurrentTenantId('tenant-456');
    const clientTenantId = prisma.getCurrentTenantId();
    console.log('Client getCurrentTenantId returns:', clientTenantId);
    console.log('Matches TenantContext:', clientTenantId === 'tenant-456');
    console.log('✅ Client getCurrentTenantId works correctly\n');

    // Test 4: Test bypass functionality
    console.log('Test 4: Bypass functionality');
    TenantContext.setCurrentTenantId('tenant-789');
    console.log('Before bypass:', TenantContext.getCurrentTenantId());
    
    await prisma.bypassTenant(async () => {
      console.log('Inside bypass:', TenantContext.getCurrentTenantId());
      return Promise.resolve();
    });
    
    console.log('After bypass:', TenantContext.getCurrentTenantId());
    console.log('✅ Bypass functionality works correctly\n');

    console.log('🎉 All multitenancy tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testMultitenancy();
}

export { testMultitenancy };