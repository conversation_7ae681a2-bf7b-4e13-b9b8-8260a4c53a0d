/**
 * Comprehensive test for TenantContextManager and multitenancy
 * Run with: npx tsx lib/test-tenant-context.ts
 */

import { prisma, TenantContext, TenantContextManager } from './prisma';

async function testTenantContextManager() {
  console.log('🧪 Testing TenantContextManager...\n');

  try {
    // Test 1: Basic context management
    console.log('Test 1: Basic context management');
    console.log('Initial context:', TenantContextManager.getCurrentContext());
    
    const context = {
      tenantId: 'tenant-123',
      tenantName: 'Test Clinic',
      userId: 'user-456',
      userRole: 'admin'
    };
    
    TenantContextManager.setGlobalContext(context);
    console.log('After setting context:', TenantContextManager.getCurrentContext());
    console.log('Tenant ID:', TenantContextManager.getCurrentTenantId());
    console.log('✅ Basic context management works\n');

    // Test 2: AsyncLocalStorage context
    console.log('Test 2: AsyncLocalStorage context');
    const asyncContext = {
      tenantId: 'tenant-async',
      tenantName: 'Async Clinic',
      userId: 'user-async',
      userRole: 'dentist'
    };

    await TenantContextManager.runWithContext(asyncContext, async () => {
      console.log('Inside async context:', TenantContextManager.getCurrentContext());
      console.log('Tenant ID in async:', TenantContextManager.getCurrentTenantId());
      
      // Test that Prisma client gets the correct tenant ID
      const clientTenantId = prisma.getCurrentTenantId();
      console.log('Prisma client tenant ID:', clientTenantId);
      console.log('Matches async context:', clientTenantId === 'tenant-async');
    });

    console.log('After async context:', TenantContextManager.getCurrentContext());
    console.log('✅ AsyncLocalStorage context works\n');

    // Test 3: Context creation helpers
    console.log('Test 3: Context creation helpers');
    const sessionData = {
      tenantId: 'tenant-session',
      tenantName: 'Session Clinic',
      userId: 'user-session',
      userRole: 'receptionist'
    };
    
    const sessionContext = TenantContextManager.createContextFromSession(sessionData);
    console.log('Session context:', sessionContext);
    console.log('✅ Context creation helpers work\n');

    // Test 4: Nested contexts
    console.log('Test 4: Nested contexts');
    TenantContextManager.setGlobalContext({ tenantId: 'global-tenant' });
    console.log('Global context:', TenantContextManager.getCurrentTenantId());

    await TenantContextManager.runWithContext(
      { tenantId: 'outer-tenant' },
      async () => {
        console.log('Outer context:', TenantContextManager.getCurrentTenantId());
        
        await TenantContextManager.runWithContext(
          { tenantId: 'inner-tenant' },
          async () => {
            console.log('Inner context:', TenantContextManager.getCurrentTenantId());
          }
        );
        
        console.log('Back to outer context:', TenantContextManager.getCurrentTenantId());
      }
    );

    console.log('Back to global context:', TenantContextManager.getCurrentTenantId());
    console.log('✅ Nested contexts work correctly\n');

    // Test 5: Bypass with new context manager
    console.log('Test 5: Bypass with new context manager');
    TenantContextManager.setGlobalContext({ tenantId: 'bypass-test' });
    console.log('Before bypass:', TenantContextManager.getCurrentTenantId());
    
    await prisma.bypassTenant(async () => {
      console.log('Inside bypass:', TenantContextManager.getCurrentTenantId());
      console.log('Prisma client in bypass:', prisma.getCurrentTenantId());
    });
    
    console.log('After bypass:', TenantContextManager.getCurrentTenantId());
    console.log('✅ Bypass with new context manager works\n');

    // Test 6: Backward compatibility
    console.log('Test 6: Backward compatibility');
    TenantContext.setCurrentTenantId('backward-compat');
    console.log('TenantContext.getCurrentTenantId():', TenantContext.getCurrentTenantId());
    console.log('TenantContextManager.getCurrentTenantId():', TenantContextManager.getCurrentTenantId());
    console.log('Match:', TenantContext.getCurrentTenantId() === TenantContextManager.getCurrentTenantId());
    console.log('✅ Backward compatibility works\n');

    console.log('🎉 All TenantContextManager tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testTenantContextManager();
}

export { testTenantContextManager };