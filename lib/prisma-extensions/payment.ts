import { Prisma, PaymentMethod, PaymentStatus, PaymentSource, CardType, ReceiptMethod } from "@prisma/client";

interface CreatePaymentInput {
  patientId: number;
  invoiceId?: number;
  paymentDate: Date;
  amount: number;
  paymentMethod: PaymentMethod;
  status?: PaymentStatus;
  paymentSource?: PaymentSource;
  transactionId?: string;
  processorResponse?: string;
  checkNumber?: string;
  bankName?: string;
  cardLastFour?: string;
  cardType?: CardType;
  processingFee?: number;
  receivedById?: number;
  processedById?: number;
  notes?: string;
  internalNotes?: string;
  receiptEmail?: string;
  receiptMethod?: ReceiptMethod;
  tenantId: string;
  createdById?: number;
}

interface ApplyToInvoiceInput {
  paymentId: number;
  invoiceId: number;
  amount?: number;
  appliedById?: number;
  notes?: string;
}

export const paymentExtension = Prisma.defineExtension({
  name: "paymentExtension",
  model: {
    payment: {
      // Generate unique payment number
      async generatePaymentNumber(tenantId: string): Promise<string> {
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        const dateStr = `${year}${month}${day}`;
        
        // Find the highest payment number for today
        const lastPayment = await this.findFirst({
          where: {
            tenantId,
            paymentNumber: {
              startsWith: `PAY${dateStr}`,
            },
          },
          orderBy: {
            paymentNumber: 'desc',
          },
        });

        let sequence = 1;
        if (lastPayment) {
          const lastSequence = parseInt(lastPayment.paymentNumber.slice(-3));
          sequence = lastSequence + 1;
        }

        return `PAY${dateStr}${sequence.toString().padStart(3, '0')}`;
      },

      // Calculate processing fee and net amount
      calculateAmounts(amount: number, processingFee: number = 0): { netAmount: number; unappliedAmount: number } {
        const netAmount = amount - processingFee;
        return {
          netAmount,
          unappliedAmount: amount, // Initially all amount is unapplied
        };
      },

      // Create payment with auto-generated payment number and calculated amounts
      async createPayment(data: CreatePaymentInput) {
        const paymentNumber = await this.generatePaymentNumber(data.tenantId);
        const processingFee = data.processingFee || 0;
        const { netAmount, unappliedAmount } = this.calculateAmounts(data.amount, processingFee);

        return this.create({
          data: {
            ...data,
            paymentNumber,
            processingFee,
            netAmount,
            unappliedAmount,
            appliedAmount: 0,
            status: data.status || PaymentStatus.COMPLETED,
            paymentSource: data.paymentSource || PaymentSource.FRONT_DESK,
            receiptMethod: data.receiptMethod || ReceiptMethod.EMAIL,
          },
        });
      },

      // Apply payment to invoice
      async applyToInvoice(data: ApplyToInvoiceInput) {
        const payment = await this.findUnique({
          where: { id: data.paymentId },
        });

        if (!payment) {
          throw new Error('Payment not found');
        }

        const invoice = await (this as any).invoice.findUnique({
          where: { id: data.invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        // Determine application amount
        const maxApplicableAmount = Math.min(
          Number(payment.unappliedAmount),
          Number(invoice.balanceDue)
        );
        
        const applicationAmount = data.amount || maxApplicableAmount;

        if (applicationAmount <= 0) {
          throw new Error('No amount available to apply');
        }

        if (applicationAmount > Number(payment.unappliedAmount)) {
          throw new Error('Application amount exceeds unapplied payment amount');
        }

        if (applicationAmount > Number(invoice.balanceDue)) {
          throw new Error('Application amount exceeds invoice balance due');
        }

        // Create payment application record
        const paymentApplication = await (this as any).paymentApplication.create({
          data: {
            tenantId: payment.tenantId,
            paymentId: data.paymentId,
            invoiceId: data.invoiceId,
            amount: applicationAmount,
            applicationDate: new Date(),
            appliedById: data.appliedById,
            notes: data.notes,
            createdById: data.appliedById,
          },
        });

        // Update payment amounts
        const newAppliedAmount = Number(payment.appliedAmount) + applicationAmount;
        const newUnappliedAmount = Number(payment.unappliedAmount) - applicationAmount;

        await this.update({
          where: { id: data.paymentId },
          data: {
            appliedAmount: newAppliedAmount,
            unappliedAmount: newUnappliedAmount,
          },
        });

        // Update invoice amounts and status
        const newAmountPaid = Number(invoice.amountPaid) + applicationAmount;
        const newBalanceDue = Number(invoice.balanceDue) - applicationAmount;
        
        let newInvoiceStatus = invoice.status;
        if (newBalanceDue <= 0) {
          newInvoiceStatus = 'PAID' as any;
        } else if (newAmountPaid > 0) {
          newInvoiceStatus = 'PARTIAL_PAYMENT' as any;
        }

        await (this as any).invoice.update({
          where: { id: data.invoiceId },
          data: {
            amountPaid: newAmountPaid,
            balanceDue: newBalanceDue,
            status: newInvoiceStatus,
          },
        });

        return paymentApplication;
      },

      // Process refund for payment
      async processRefund(
        paymentId: number,
        refundAmount: number,
        refundReason: string,
        updatedById?: number
      ) {
        const payment = await this.findUnique({
          where: { id: paymentId },
        });

        if (!payment) {
          throw new Error('Payment not found');
        }

        if (payment.isRefunded) {
          throw new Error('Payment has already been refunded');
        }

        if (refundAmount <= 0 || refundAmount > Number(payment.amount)) {
          throw new Error('Invalid refund amount');
        }

        const isFullRefund = refundAmount === Number(payment.amount);
        const newStatus = isFullRefund ? PaymentStatus.REFUNDED : payment.status;

        return this.update({
          where: { id: paymentId },
          data: {
            isRefunded: true,
            refundDate: new Date(),
            refundAmount,
            refundReason,
            status: newStatus,
            updatedById,
          },
        });
      },

      // Get payments by status
      async getPaymentsByStatus(tenantId: string, status: PaymentStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
            invoice: true,
            receivedBy: true,
            processedBy: true,
          },
          orderBy: {
            paymentDate: 'desc',
          },
        });
      },

      // Get payments for a patient
      async getPaymentsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            patientId,
          },
          include: {
            invoice: true,
            applications: {
              include: {
                invoice: true,
              },
            },
            receivedBy: true,
            processedBy: true,
          },
          orderBy: {
            paymentDate: 'desc',
          },
        });
      },

      // Get payments requiring deposit
      async getPaymentsRequiringDeposit(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            paymentMethod: {
              in: [PaymentMethod.CASH, PaymentMethod.CHECK],
            },
            status: PaymentStatus.COMPLETED,
            isDeposited: false,
          },
          include: {
            patient: true,
            receivedBy: true,
          },
          orderBy: {
            paymentDate: 'asc',
          },
        });
      },

      // Get undeposited payments by date range
      async getUndepositedPayments(tenantId: string, startDate: Date, endDate: Date) {
        return this.findMany({
          where: {
            tenantId,
            paymentDate: {
              gte: startDate,
              lte: endDate,
            },
            isDeposited: false,
            status: PaymentStatus.COMPLETED,
          },
          include: {
            patient: true,
            receivedBy: true,
          },
          orderBy: {
            paymentDate: 'asc',
          },
        });
      },

      // Mark payments as deposited
      async markAsDeposited(
        paymentIds: number[],
        depositDate: Date,
        depositBatch: string,
        updatedById?: number
      ) {
        return this.updateMany({
          where: {
            id: {
              in: paymentIds,
            },
          },
          data: {
            isDeposited: true,
            depositDate,
            depositBatch,
            updatedById,
          },
        });
      },

      // Get payment summary statistics
      async getPaymentSummary(tenantId: string, startDate?: Date, endDate?: Date) {
        const whereClause: any = { tenantId };
        
        if (startDate || endDate) {
          whereClause.paymentDate = {};
          if (startDate) whereClause.paymentDate.gte = startDate;
          if (endDate) whereClause.paymentDate.lte = endDate;
        }

        const payments = await this.findMany({
          where: whereClause,
        });

        const summary = {
          totalPayments: payments.length,
          totalAmount: 0,
          totalApplied: 0,
          totalUnapplied: 0,
          totalRefunded: 0,
          methodBreakdown: {} as Record<PaymentMethod, { count: number; amount: number }>,
          statusBreakdown: {} as Record<PaymentStatus, { count: number; amount: number }>,
        };

        // Initialize breakdowns
        Object.values(PaymentMethod).forEach(method => {
          summary.methodBreakdown[method] = { count: 0, amount: 0 };
        });

        Object.values(PaymentStatus).forEach(status => {
          summary.statusBreakdown[status] = { count: 0, amount: 0 };
        });

        payments.forEach(payment => {
          const amount = Number(payment.amount);
          const appliedAmount = Number(payment.appliedAmount);
          const unappliedAmount = Number(payment.unappliedAmount);
          const refundAmount = Number(payment.refundAmount || 0);

          summary.totalAmount += amount;
          summary.totalApplied += appliedAmount;
          summary.totalUnapplied += unappliedAmount;
          summary.totalRefunded += refundAmount;

          summary.methodBreakdown[payment.paymentMethod].count++;
          summary.methodBreakdown[payment.paymentMethod].amount += amount;

          summary.statusBreakdown[payment.status].count++;
          summary.statusBreakdown[payment.status].amount += amount;
        });

        return summary;
      },

      // Validate payment data
      async validatePayment(data: Partial<CreatePaymentInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.paymentDate) {
          errors.push('Payment date is required');
        }

        if (!data.amount || data.amount <= 0) {
          errors.push('Payment amount must be greater than 0');
        }

        if (!data.paymentMethod) {
          errors.push('Payment method is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate processing fee
        if (data.processingFee !== undefined && data.processingFee < 0) {
          errors.push('Processing fee cannot be negative');
        }

        if (data.processingFee !== undefined && data.amount !== undefined && data.processingFee >= data.amount) {
          errors.push('Processing fee cannot be greater than or equal to payment amount');
        }

        // Validate card-specific fields
        if (data.paymentMethod === PaymentMethod.CREDIT_CARD || data.paymentMethod === PaymentMethod.DEBIT_CARD) {
          if (data.cardLastFour && (data.cardLastFour.length !== 4 || !/^\d{4}$/.test(data.cardLastFour))) {
            errors.push('Card last four digits must be exactly 4 digits');
          }
        }

        // Validate check-specific fields
        if (data.paymentMethod === PaymentMethod.CHECK) {
          if (!data.checkNumber) {
            errors.push('Check number is required for check payments');
          }
        }

        // Validate payment date is not too far in the future
        if (data.paymentDate) {
          const maxFutureDate = new Date();
          maxFutureDate.setDate(maxFutureDate.getDate() + 1);
          
          if (data.paymentDate > maxFutureDate) {
            errors.push('Payment date cannot be more than 1 day in the future');
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Send receipt
      async sendReceipt(
        paymentId: number,
        receiptEmail?: string,
        receiptMethod?: ReceiptMethod,
        updatedById?: number
      ) {
        const payment = await this.findUnique({
          where: { id: paymentId },
          include: {
            patient: true,
          },
        });

        if (!payment) {
          throw new Error('Payment not found');
        }

        const updateData: any = {
          receiptSent: true,
          updatedById,
        };

        if (receiptEmail) {
          updateData.receiptEmail = receiptEmail;
        }

        if (receiptMethod) {
          updateData.receiptMethod = receiptMethod;
        }

        return this.update({
          where: { id: paymentId },
          data: updateData,
        });
      },

      // Get payments by date range
      async getPaymentsByDateRange(
        tenantId: string,
        startDate: Date,
        endDate: Date,
        paymentMethod?: PaymentMethod
      ) {
        const where: any = {
          tenantId,
          paymentDate: {
            gte: startDate,
            lte: endDate,
          },
        };

        if (paymentMethod) {
          where.paymentMethod = paymentMethod;
        }

        return this.findMany({
          where,
          include: {
            patient: true,
            invoice: true,
            receivedBy: true,
          },
          orderBy: {
            paymentDate: 'desc',
          },
        });
      },

      // Get today's payments
      async getTodaysPayments(tenantId: string) {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

        return this.getPaymentsByDateRange(tenantId, startOfDay, endOfDay);
      },

      // Enhanced refund processing with payment application tracking
      async processRefundWithApplications(
        paymentId: number,
        refundAmount: number,
        refundReason: string,
        updatedById?: number
      ) {
        const payment = await this.findUnique({
          where: { id: paymentId },
          include: {
            applications: {
              include: {
                invoice: true,
              },
            },
          },
        });

        if (!payment) {
          throw new Error('Payment not found');
        }

        if (payment.isRefunded) {
          throw new Error('Payment has already been refunded');
        }

        if (refundAmount <= 0 || refundAmount > Number(payment.amount)) {
          throw new Error('Invalid refund amount');
        }

        // If payment has been applied to invoices, we need to reverse those applications
        if (payment.applications.length > 0 && refundAmount > Number(payment.unappliedAmount)) {
          const applicationsToReverse = [];
          let remainingRefundAmount = refundAmount - Number(payment.unappliedAmount);

          // Sort applications by date (most recent first) to reverse in LIFO order
          const sortedApplications = payment.applications.sort(
            (a, b) => new Date(b.applicationDate).getTime() - new Date(a.applicationDate).getTime()
          );

          for (const application of sortedApplications) {
            if (remainingRefundAmount <= 0) break;

            const applicationAmount = Number(application.amount);
            const reverseAmount = Math.min(applicationAmount, remainingRefundAmount);

            applicationsToReverse.push({
              application,
              reverseAmount,
            });

            remainingRefundAmount -= reverseAmount;
          }

          // Reverse the payment applications
          for (const { application, reverseAmount } of applicationsToReverse) {
            // Update invoice amounts
            const invoice = application.invoice;
            const newAmountPaid = Number(invoice.amountPaid) - reverseAmount;
            const newBalanceDue = Number(invoice.balanceDue) + reverseAmount;

            // Update invoice status
            let newInvoiceStatus = invoice.status;
            if (newAmountPaid === 0) {
              newInvoiceStatus = 'SENT' as any; // or appropriate status
            } else if (newBalanceDue > 0) {
              newInvoiceStatus = 'PARTIAL_PAYMENT' as any;
            }

            await (this as any).invoice.update({
              where: { id: application.invoiceId },
              data: {
                amountPaid: newAmountPaid,
                balanceDue: newBalanceDue,
                status: newInvoiceStatus,
              },
            });

            // Remove or update the payment application
            if (reverseAmount === Number(application.amount)) {
              // Remove the entire application
              await (this as any).paymentApplication.delete({
                where: { id: application.id },
              });
            } else {
              // Update the application with reduced amount
              await (this as any).paymentApplication.update({
                where: { id: application.id },
                data: {
                  amount: Number(application.amount) - reverseAmount,
                },
              });
            }
          }
        }

        // Update payment with refund information
        const isFullRefund = refundAmount === Number(payment.amount);
        const newStatus = isFullRefund ? PaymentStatus.REFUNDED : payment.status;
        const newAppliedAmount = Math.max(0, Number(payment.appliedAmount) - (refundAmount - Number(payment.unappliedAmount)));
        const newUnappliedAmount = isFullRefund ? 0 : Number(payment.amount) - refundAmount - newAppliedAmount;

        return this.update({
          where: { id: paymentId },
          data: {
            isRefunded: true,
            refundDate: new Date(),
            refundAmount,
            refundReason,
            status: newStatus,
            appliedAmount: newAppliedAmount,
            unappliedAmount: newUnappliedAmount,
            updatedById,
          },
        });
      },

      // Get refunded payments
      async getRefundedPayments(tenantId: string, startDate?: Date, endDate?: Date) {
        const whereClause: any = {
          tenantId,
          isRefunded: true,
        };

        if (startDate || endDate) {
          whereClause.refundDate = {};
          if (startDate) whereClause.refundDate.gte = startDate;
          if (endDate) whereClause.refundDate.lte = endDate;
        }

        return this.findMany({
          where: whereClause,
          include: {
            patient: true,
            invoice: true,
            processedBy: true,
          },
          orderBy: {
            refundDate: 'desc',
          },
        });
      },
    },
  },
  result: {
    payment: {
      // Check if payment was successful
      isSuccessful: {
        needs: { status: true },
        compute(payment) {
          return payment.status === PaymentStatus.COMPLETED;
        },
      },

      // Check if payment requires deposit
      requiresDeposit: {
        needs: { paymentMethod: true, isDeposited: true, status: true },
        compute(payment) {
          const depositMethods = [PaymentMethod.CASH, PaymentMethod.CHECK];
          return (
            depositMethods.includes(payment.paymentMethod) &&
            !payment.isDeposited &&
            payment.status === PaymentStatus.COMPLETED
          );
        },
      },

      // Check if payment is electronic
      isElectronic: {
        needs: { paymentMethod: true },
        compute(payment) {
          const electronicMethods = [
            PaymentMethod.CREDIT_CARD,
            PaymentMethod.DEBIT_CARD,
            PaymentMethod.BANK_TRANSFER,
            PaymentMethod.ACH,
            PaymentMethod.PAYPAL,
            PaymentMethod.VENMO,
            PaymentMethod.APPLE_PAY,
            PaymentMethod.GOOGLE_PAY,
          ];
          return electronicMethods.includes(payment.paymentMethod);
        },
      },

      // Get user-friendly payment method display name
      paymentMethodDisplayName: {
        needs: { paymentMethod: true, cardType: true, cardLastFour: true, checkNumber: true },
        compute(payment) {
          if (payment.paymentMethod === PaymentMethod.CREDIT_CARD && payment.cardType && payment.cardLastFour) {
            const cardTypeDisplay = payment.cardType.charAt(0) + payment.cardType.slice(1).toLowerCase();
            return `${cardTypeDisplay} ending in ${payment.cardLastFour}`;
          } else if (payment.paymentMethod === PaymentMethod.CHECK && payment.checkNumber) {
            return `Check #${payment.checkNumber}`;
          } else {
            // Convert enum to display format
            return payment.paymentMethod.split('_').map(word => 
              word.charAt(0) + word.slice(1).toLowerCase()
            ).join(' ');
          }
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(payment) {
          switch (payment.status) {
            case PaymentStatus.PENDING:
              return 'blue';
            case PaymentStatus.PROCESSING:
              return 'orange';
            case PaymentStatus.COMPLETED:
              return 'green';
            case PaymentStatus.FAILED:
              return 'red';
            case PaymentStatus.CANCELLED:
              return 'gray';
            case PaymentStatus.REFUNDED:
              return 'orange';
            case PaymentStatus.DISPUTED:
              return 'red';
            case PaymentStatus.CHARGEBACK:
              return 'red';
            default:
              return 'gray';
          }
        },
      },

      // Format payment number for display
      displayPaymentNumber: {
        needs: { paymentNumber: true },
        compute(payment) {
          return `#${payment.paymentNumber}`;
        },
      },

      // Check if payment has unapplied amount
      hasUnappliedAmount: {
        needs: { unappliedAmount: true },
        compute(payment) {
          return Number(payment.unappliedAmount) > 0;
        },
      },

      // Check if payment is fully applied
      isFullyApplied: {
        needs: { unappliedAmount: true },
        compute(payment) {
          return Number(payment.unappliedAmount) === 0;
        },
      },

      // Get application percentage
      applicationPercentage: {
        needs: { amount: true, appliedAmount: true },
        compute(payment) {
          const totalAmount = Number(payment.amount);
          const appliedAmount = Number(payment.appliedAmount);
          
          if (totalAmount === 0) return 0;
          return Math.round((appliedAmount / totalAmount) * 100);
        },
      },

      // Check if payment can be refunded
      canBeRefunded: {
        needs: { status: true, isRefunded: true },
        compute(payment) {
          return (
            payment.status === PaymentStatus.COMPLETED &&
            !payment.isRefunded
          );
        },
      },

      // Format amount for display
      displayAmount: {
        needs: { amount: true },
        compute(payment) {
          return `$${Number(payment.amount).toFixed(2)}`;
        },
      },

      // Format net amount for display
      displayNetAmount: {
        needs: { netAmount: true },
        compute(payment) {
          return `$${Number(payment.netAmount).toFixed(2)}`;
        },
      },
    },
  },
});