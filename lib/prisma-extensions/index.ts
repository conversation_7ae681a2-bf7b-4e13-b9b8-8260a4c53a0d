import { multitenancyExtension } from './multitenancy';
import { userExtension } from './user';
import { appointmentExtension } from './appointment';
import { invoiceExtension } from './invoice';
import { paymentExtension } from './payment';
import { adjustmentExtension } from './adjustment';

// Export all extensions for easy importing
export { multitenancyExtension, TenantContext } from './multitenancy';
export { userExtension } from './user';
export { appointmentExtension } from './appointment';
export { invoiceExtension } from './invoice';
export { paymentExtension } from './payment';
export { adjustmentExtension } from './adjustment';
export { TenantContextManager } from '../tenant-context';

// Combined extensions array for easy application to Prisma client
export const allExtensions = [
  multitenancyExtension,
  userExtension,
  appointmentExtension,
  invoiceExtension,
  paymentExtension,
  adjustmentExtension,
  // Additional model extensions will be added here as they are implemented
];