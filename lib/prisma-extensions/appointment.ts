import { Prisma, AppointmentType, AppointmentStatus, AppointmentPriority, RecallType, ConfirmationMethod, CancelledBy } from "@prisma/client";

interface CreateAppointmentInput {
  patientId: number;
  appointmentDate: Date;
  durationMinutes?: number;
  appointmentType?: AppointmentType;
  status?: AppointmentStatus;
  priority?: AppointmentPriority;
  primaryProviderId?: number;
  chiefComplaint?: string;
  procedureCodes?: string;
  clinicalNotes?: string;
  isRecallAppointment?: boolean;
  recallType?: RecallType;
  recallIntervalMonths?: number;
  treatmentRoom?: string;
  specialEquipmentNeeded?: string;
  specialInstructions?: string;
  preMedicationRequired?: boolean;
  preMedicationNotes?: string;
  wheelchairAccessible?: boolean;
  interpreterNeeded?: boolean;
  interpreterLanguage?: string;
  estimatedFee?: number;
  insurancePreAuth?: string;
  followUpNeeded?: boolean;
  followUpIntervalWeeks?: number;
  tenantId: string;
  createdById?: number;
}

export const appointmentExtension = Prisma.defineExtension({
  name: "appointmentExtension",
  model: {
    appointment: {
      // Generate unique appointment number
      async generateAppointmentNumber(tenantId: string): Promise<string> {
        const today = new Date();
        const year = today.getFullYear().toString().slice(-2);
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        
        // Find the highest appointment number for today
        const lastAppointment = await this.findFirst({
          where: {
            tenantId,
            appointmentNumber: {
              startsWith: `APT${year}${month}${day}`,
            },
          },
          orderBy: {
            appointmentNumber: 'desc',
          },
        });

        let sequence = 1;
        if (lastAppointment) {
          const lastSequence = parseInt(lastAppointment.appointmentNumber.slice(-4));
          sequence = lastSequence + 1;
        }

        return `APT${year}${month}${day}${sequence.toString().padStart(4, '0')}`;
      },

      // Calculate end time from appointment date and duration
      calculateEndTime(appointmentDate: Date, durationMinutes: number): Date {
        const endTime = new Date(appointmentDate);
        endTime.setMinutes(endTime.getMinutes() + durationMinutes);
        return endTime;
      },

      // Create appointment with auto-generated appointment number and end time
      async createAppointment(data: CreateAppointmentInput) {
        const appointmentNumber = await this.generateAppointmentNumber(data.tenantId);
        const durationMinutes = data.durationMinutes || 60;
        const endTime = this.calculateEndTime(data.appointmentDate, durationMinutes);

        return this.create({
          data: {
            ...data,
            appointmentNumber,
            durationMinutes,
            endTime,
            status: data.status || AppointmentStatus.SCHEDULED,
            priority: data.priority || AppointmentPriority.ROUTINE,
            appointmentType: data.appointmentType || AppointmentType.CONSULTATION,
          },
        });
      },

      // Get appointments for a specific date range
      async getAppointmentsByDateRange(
        tenantId: string,
        startDate: Date,
        endDate: Date,
        providerId?: number
      ) {
        const where: any = {
          tenantId,
          appointmentDate: {
            gte: startDate,
            lte: endDate,
          },
        };

        if (providerId) {
          where.primaryProviderId = providerId;
        }

        return this.findMany({
          where,
          include: {
            patient: true,
            primaryProvider: true,
            assistingProviders: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
        });
      },

      // Get today's appointments
      async getTodaysAppointments(tenantId: string, providerId?: number) {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

        return this.getAppointmentsByDateRange(tenantId, startOfDay, endOfDay, providerId);
      },

      // Get upcoming appointments for a patient
      async getUpcomingAppointmentsForPatient(tenantId: string, patientId: number, limit: number = 10) {
        const now = new Date();
        
        return this.findMany({
          where: {
            tenantId,
            patientId,
            appointmentDate: {
              gte: now,
            },
            status: {
              in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED],
            },
          },
          include: {
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
          take: limit,
        });
      },

      // Get overdue appointments
      async getOverdueAppointments(tenantId: string) {
        const now = new Date();
        
        return this.findMany({
          where: {
            tenantId,
            appointmentDate: {
              lt: now,
            },
            status: {
              in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED],
            },
          },
          include: {
            patient: true,
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'desc',
          },
        });
      },

      // Get appointments by status
      async getAppointmentsByStatus(tenantId: string, status: AppointmentStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
        });
      },

      // Validate appointment business rules
      async validateAppointment(data: Partial<CreateAppointmentInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.appointmentDate) {
          errors.push('Appointment date is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate appointment date is not in the past (for new appointments)
        if (data.appointmentDate && !data.id) {
          const now = new Date();
          if (data.appointmentDate < now) {
            errors.push('Appointment date cannot be in the past');
          }
        }

        // Validate duration
        if (data.durationMinutes && (data.durationMinutes < 15 || data.durationMinutes > 480)) {
          errors.push('Duration must be between 15 minutes and 8 hours');
        }

        // Validate recall appointment fields
        if (data.isRecallAppointment) {
          if (!data.recallType) {
            errors.push('Recall type is required for recall appointments');
          }
          if (!data.recallIntervalMonths || data.recallIntervalMonths < 1 || data.recallIntervalMonths > 24) {
            errors.push('Recall interval must be between 1 and 24 months');
          }
        }

        // Check for scheduling conflicts (if provider and date are specified)
        if (data.tenantId && data.primaryProviderId && data.appointmentDate && data.durationMinutes) {
          const endTime = this.calculateEndTime(data.appointmentDate, data.durationMinutes);
          
          const conflictingAppointments = await this.findMany({
            where: {
              tenantId: data.tenantId,
              primaryProviderId: data.primaryProviderId,
              id: data.id ? { not: data.id } : undefined,
              status: {
                in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS],
              },
              OR: [
                {
                  appointmentDate: {
                    gte: data.appointmentDate,
                    lt: endTime,
                  },
                },
                {
                  endTime: {
                    gt: data.appointmentDate,
                    lte: endTime,
                  },
                },
                {
                  AND: [
                    {
                      appointmentDate: {
                        lte: data.appointmentDate,
                      },
                    },
                    {
                      endTime: {
                        gte: endTime,
                      },
                    },
                  ],
                },
              ],
            },
          });

          if (conflictingAppointments.length > 0) {
            errors.push('Provider has conflicting appointments at this time');
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Check if appointment can be cancelled (business logic)
      async canBeCancelledLogic(appointmentId: number) {
        const appointment = await this.findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          return { canCancel: false, reason: 'Appointment not found' };
        }

        const now = new Date();
        const appointmentDate = new Date(appointment.appointmentDate);

        // Cannot cancel completed, already cancelled, or no-show appointments
        if ([
          AppointmentStatus.COMPLETED,
          AppointmentStatus.CANCELLED,
          AppointmentStatus.NO_SHOW,
          AppointmentStatus.LATE_CANCELLATION,
        ].includes(appointment.status)) {
          return { canCancel: false, reason: 'Appointment is already completed, cancelled, or marked as no-show' };
        }

        // Cannot cancel appointments that have already started
        if (appointment.status === AppointmentStatus.IN_PROGRESS) {
          return { canCancel: false, reason: 'Appointment is currently in progress' };
        }

        // Cannot cancel appointments in the past
        if (appointmentDate < now) {
          return { canCancel: false, reason: 'Cannot cancel past appointments' };
        }

        return { canCancel: true, reason: null };
      },

      // Check if appointment can be rescheduled (business logic)
      async canBeRescheduledLogic(appointmentId: number) {
        const appointment = await this.findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          return { canReschedule: false, reason: 'Appointment not found' };
        }

        // Cannot reschedule completed, cancelled, or no-show appointments
        if ([
          AppointmentStatus.COMPLETED,
          AppointmentStatus.CANCELLED,
          AppointmentStatus.NO_SHOW,
          AppointmentStatus.LATE_CANCELLATION,
        ].includes(appointment.status)) {
          return { canReschedule: false, reason: 'Appointment is already completed, cancelled, or marked as no-show' };
        }

        // Cannot reschedule appointments that have already started
        if (appointment.status === AppointmentStatus.IN_PROGRESS) {
          return { canReschedule: false, reason: 'Appointment is currently in progress' };
        }

        return { canReschedule: true, reason: null };
      },

      // Cancel appointment with reason and tracking
      async cancelAppointment(
        appointmentId: number,
        reason: string,
        cancelledBy: CancelledBy,
        updatedById?: number
      ) {
        const canCancelResult = await this.canBeCancelledLogic(appointmentId);
        if (!canCancelResult.canCancel) {
          throw new Error(canCancelResult.reason || 'Cannot cancel appointment');
        }

        const appointment = await this.findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          throw new Error('Appointment not found');
        }

        const now = new Date();
        const appointmentDate = new Date(appointment.appointmentDate);
        
        // Determine if this is a late cancellation (less than 24 hours)
        const hoursDifference = (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60);
        const isLateCancellation = hoursDifference < 24;

        return this.update({
          where: { id: appointmentId },
          data: {
            status: isLateCancellation ? AppointmentStatus.LATE_CANCELLATION : AppointmentStatus.CANCELLED,
            cancellationReason: reason,
            cancelledBy,
            cancellationDate: now,
            updatedById,
          },
        });
      },

      // Reschedule appointment to new date/time
      async rescheduleAppointment(
        appointmentId: number,
        newAppointmentDate: Date,
        newDurationMinutes?: number,
        updatedById?: number
      ) {
        const canRescheduleResult = await this.canBeRescheduledLogic(appointmentId);
        if (!canRescheduleResult.canReschedule) {
          throw new Error(canRescheduleResult.reason || 'Cannot reschedule appointment');
        }

        const appointment = await this.findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          throw new Error('Appointment not found');
        }

        const durationMinutes = newDurationMinutes || appointment.durationMinutes;
        const newEndTime = this.calculateEndTime(newAppointmentDate, durationMinutes);

        // Validate the new time slot
        const validation = await this.validateAppointment({
          id: appointmentId,
          tenantId: appointment.tenantId,
          patientId: appointment.patientId,
          appointmentDate: newAppointmentDate,
          durationMinutes,
          primaryProviderId: appointment.primaryProviderId || undefined,
        });

        if (!validation.isValid) {
          throw new Error(`Cannot reschedule: ${validation.errors.join(', ')}`);
        }

        // Create new appointment with rescheduled status
        const rescheduledAppointment = await this.create({
          data: {
            tenantId: appointment.tenantId,
            appointmentNumber: await this.generateAppointmentNumber(appointment.tenantId),
            patientId: appointment.patientId,
            appointmentDate: newAppointmentDate,
            durationMinutes,
            endTime: newEndTime,
            appointmentType: appointment.appointmentType,
            status: AppointmentStatus.SCHEDULED,
            priority: appointment.priority,
            primaryProviderId: appointment.primaryProviderId,
            chiefComplaint: appointment.chiefComplaint,
            procedureCodes: appointment.procedureCodes,
            clinicalNotes: appointment.clinicalNotes,
            isRecallAppointment: appointment.isRecallAppointment,
            recallType: appointment.recallType,
            recallIntervalMonths: appointment.recallIntervalMonths,
            treatmentRoom: appointment.treatmentRoom,
            specialEquipmentNeeded: appointment.specialEquipmentNeeded,
            specialInstructions: appointment.specialInstructions,
            preMedicationRequired: appointment.preMedicationRequired,
            preMedicationNotes: appointment.preMedicationNotes,
            wheelchairAccessible: appointment.wheelchairAccessible,
            interpreterNeeded: appointment.interpreterNeeded,
            interpreterLanguage: appointment.interpreterLanguage,
            estimatedFee: appointment.estimatedFee,
            insurancePreAuth: appointment.insurancePreAuth,
            followUpNeeded: appointment.followUpNeeded,
            followUpIntervalWeeks: appointment.followUpIntervalWeeks,
            rescheduledFromId: appointmentId,
            createdById: updatedById,
          },
        });

        // Update original appointment status
        await this.update({
          where: { id: appointmentId },
          data: {
            status: AppointmentStatus.RESCHEDULED,
            updatedById,
          },
        });

        return rescheduledAppointment;
      },

      // Update appointment status with validation
      async updateAppointmentStatus(
        appointmentId: number,
        newStatus: AppointmentStatus,
        updatedById?: number
      ) {
        const appointment = await this.findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          throw new Error('Appointment not found');
        }

        // Validate status transitions
        const validTransitions: Record<AppointmentStatus, AppointmentStatus[]> = {
          [AppointmentStatus.SCHEDULED]: [
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.CANCELLED,
            AppointmentStatus.RESCHEDULED,
            AppointmentStatus.NO_SHOW,
          ],
          [AppointmentStatus.CONFIRMED]: [
            AppointmentStatus.CHECKED_IN,
            AppointmentStatus.CANCELLED,
            AppointmentStatus.RESCHEDULED,
            AppointmentStatus.NO_SHOW,
            AppointmentStatus.LATE_CANCELLATION,
          ],
          [AppointmentStatus.CHECKED_IN]: [
            AppointmentStatus.IN_PROGRESS,
            AppointmentStatus.NO_SHOW,
          ],
          [AppointmentStatus.IN_PROGRESS]: [
            AppointmentStatus.COMPLETED,
          ],
          [AppointmentStatus.COMPLETED]: [], // Final state
          [AppointmentStatus.NO_SHOW]: [], // Final state
          [AppointmentStatus.CANCELLED]: [], // Final state
          [AppointmentStatus.RESCHEDULED]: [], // Final state
          [AppointmentStatus.LATE_CANCELLATION]: [], // Final state
        };

        const allowedTransitions = validTransitions[appointment.status] || [];
        if (!allowedTransitions.includes(newStatus)) {
          throw new Error(`Cannot transition from ${appointment.status} to ${newStatus}`);
        }

        const updateData: any = {
          status: newStatus,
          updatedById,
        };

        // Set timestamps based on status
        const now = new Date();
        switch (newStatus) {
          case AppointmentStatus.CHECKED_IN:
            updateData.checkInTime = now;
            break;
          case AppointmentStatus.IN_PROGRESS:
            updateData.actualStartTime = now;
            break;
          case AppointmentStatus.COMPLETED:
            updateData.actualEndTime = now;
            break;
        }

        return this.update({
          where: { id: appointmentId },
          data: updateData,
        });
      },

      // Calculate next recall date based on recall type and interval
      calculateNextRecallDate(lastAppointmentDate: Date, recallIntervalMonths: number): Date {
        const nextRecallDate = new Date(lastAppointmentDate);
        nextRecallDate.setMonth(nextRecallDate.getMonth() + recallIntervalMonths);
        return nextRecallDate;
      },

      // Get default recall interval for recall type
      getDefaultRecallInterval(recallType: RecallType): number {
        switch (recallType) {
          case RecallType.ROUTINE_CLEANING:
            return 6; // 6 months
          case RecallType.PERIODONTAL_MAINTENANCE:
            return 3; // 3 months
          case RecallType.ORTHODONTIC_ADJUSTMENT:
            return 1; // 1 month
          case RecallType.POST_TREATMENT:
            return 1; // 1 month
          case RecallType.FLUORIDE_TREATMENT:
            return 6; // 6 months
          case RecallType.ORAL_CANCER_SCREENING:
            return 12; // 12 months
          case RecallType.CUSTOM:
            return 6; // Default to 6 months
          default:
            return 6;
        }
      },

      // Create next recall appointment
      async createNextRecall(
        completedAppointmentId: number,
        recallType?: RecallType,
        recallIntervalMonths?: number,
        createdById?: number
      ) {
        const completedAppointment = await this.findUnique({
          where: { id: completedAppointmentId },
          include: {
            patient: true,
          },
        });

        if (!completedAppointment) {
          throw new Error('Completed appointment not found');
        }

        if (completedAppointment.status !== AppointmentStatus.COMPLETED) {
          throw new Error('Can only create recall appointments from completed appointments');
        }

        // Use provided recall type or default to routine cleaning
        const nextRecallType = recallType || RecallType.ROUTINE_CLEANING;
        
        // Use provided interval or get default for recall type
        const intervalMonths = recallIntervalMonths || this.getDefaultRecallInterval(nextRecallType);
        
        // Calculate next recall date
        const nextRecallDate = this.calculateNextRecallDate(
          completedAppointment.appointmentDate,
          intervalMonths
        );

        // Create recall appointment
        const recallAppointment = await this.createAppointment({
          tenantId: completedAppointment.tenantId,
          patientId: completedAppointment.patientId,
          appointmentDate: nextRecallDate,
          durationMinutes: 60, // Default duration for recall
          appointmentType: AppointmentType.RECALL,
          status: AppointmentStatus.SCHEDULED,
          priority: AppointmentPriority.ROUTINE,
          primaryProviderId: completedAppointment.primaryProviderId || undefined,
          isRecallAppointment: true,
          recallType: nextRecallType,
          recallIntervalMonths: intervalMonths,
          createdById,
        });

        return recallAppointment;
      },

      // Get patients due for recall
      async getPatientsDueForRecall(tenantId: string, daysAhead: number = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() + daysAhead);

        // Find patients with completed appointments who don't have future recall appointments
        const patientsWithCompletedAppointments = await this.findMany({
          where: {
            tenantId,
            status: AppointmentStatus.COMPLETED,
            appointmentDate: {
              gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // Within last year
            },
          },
          include: {
            patient: true,
          },
          orderBy: {
            appointmentDate: 'desc',
          },
        });

        const patientRecallInfo = new Map();

        // Group by patient and find their last completed appointment
        for (const appointment of patientsWithCompletedAppointments) {
          const patientId = appointment.patientId;
          
          if (!patientRecallInfo.has(patientId)) {
            // Check if patient already has a future recall appointment
            const futureRecall = await this.findFirst({
              where: {
                tenantId,
                patientId,
                isRecallAppointment: true,
                appointmentDate: {
                  gte: new Date(),
                },
                status: {
                  in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED],
                },
              },
            });

            if (!futureRecall) {
              // Calculate when recall is due (default 6 months)
              const recallInterval = appointment.recallIntervalMonths || 6;
              const recallDueDate = this.calculateNextRecallDate(
                appointment.appointmentDate,
                recallInterval
              );

              if (recallDueDate <= cutoffDate) {
                patientRecallInfo.set(patientId, {
                  patient: appointment.patient,
                  lastAppointment: appointment,
                  recallDueDate,
                  daysPastDue: Math.floor(
                    (new Date().getTime() - recallDueDate.getTime()) / (1000 * 60 * 60 * 24)
                  ),
                });
              }
            }
          }
        }

        return Array.from(patientRecallInfo.values());
      },

      // Get recall appointments for a date range
      async getRecallAppointments(
        tenantId: string,
        startDate: Date,
        endDate: Date,
        recallType?: RecallType
      ) {
        const where: any = {
          tenantId,
          isRecallAppointment: true,
          appointmentDate: {
            gte: startDate,
            lte: endDate,
          },
        };

        if (recallType) {
          where.recallType = recallType;
        }

        return this.findMany({
          where,
          include: {
            patient: true,
            primaryProvider: true,
          },
          orderBy: {
            appointmentDate: 'asc',
          },
        });
      },

      // Validate recall appointment data
      async validateRecallAppointment(data: {
        recallType: RecallType;
        recallIntervalMonths: number;
        patientId: number;
        tenantId: string;
      }) {
        const errors: string[] = [];

        // Validate recall interval
        if (data.recallIntervalMonths < 1 || data.recallIntervalMonths > 24) {
          errors.push('Recall interval must be between 1 and 24 months');
        }

        // Validate that patient doesn't already have a future recall appointment
        const existingRecall = await this.findFirst({
          where: {
            tenantId: data.tenantId,
            patientId: data.patientId,
            isRecallAppointment: true,
            appointmentDate: {
              gte: new Date(),
            },
            status: {
              in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED],
            },
          },
        });

        if (existingRecall) {
          errors.push('Patient already has a future recall appointment scheduled');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Update recall appointment interval
      async updateRecallInterval(
        appointmentId: number,
        newIntervalMonths: number,
        updatedById?: number
      ) {
        const appointment = await this.findUnique({
          where: { id: appointmentId },
        });

        if (!appointment) {
          throw new Error('Appointment not found');
        }

        if (!appointment.isRecallAppointment) {
          throw new Error('Can only update recall interval for recall appointments');
        }

        if (newIntervalMonths < 1 || newIntervalMonths > 24) {
          throw new Error('Recall interval must be between 1 and 24 months');
        }

        return this.update({
          where: { id: appointmentId },
          data: {
            recallIntervalMonths: newIntervalMonths,
            updatedById,
          },
        });
      },
    },
  },
  result: {
    appointment: {
      // Format duration for display
      durationDisplay: {
        needs: { durationMinutes: true },
        compute(appointment) {
          const hours = Math.floor(appointment.durationMinutes / 60);
          const minutes = appointment.durationMinutes % 60;
          
          if (hours === 0) {
            return `${minutes}m`;
          } else if (minutes === 0) {
            return `${hours}h`;
          } else {
            return `${hours}h ${minutes}m`;
          }
        },
      },

      // Check if appointment is today
      isToday: {
        needs: { appointmentDate: true },
        compute(appointment) {
          const today = new Date();
          const appointmentDate = new Date(appointment.appointmentDate);
          
          return (
            appointmentDate.getDate() === today.getDate() &&
            appointmentDate.getMonth() === today.getMonth() &&
            appointmentDate.getFullYear() === today.getFullYear()
          );
        },
      },

      // Check if appointment is overdue
      isOverdue: {
        needs: { appointmentDate: true, status: true },
        compute(appointment) {
          const now = new Date();
          const appointmentDate = new Date(appointment.appointmentDate);
          
          return (
            appointmentDate < now &&
            (appointment.status === AppointmentStatus.SCHEDULED || 
             appointment.status === AppointmentStatus.CONFIRMED)
          );
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(appointment) {
          switch (appointment.status) {
            case AppointmentStatus.SCHEDULED:
              return 'blue';
            case AppointmentStatus.CONFIRMED:
              return 'green';
            case AppointmentStatus.CHECKED_IN:
              return 'yellow';
            case AppointmentStatus.IN_PROGRESS:
              return 'orange';
            case AppointmentStatus.COMPLETED:
              return 'green';
            case AppointmentStatus.NO_SHOW:
              return 'red';
            case AppointmentStatus.CANCELLED:
              return 'gray';
            case AppointmentStatus.RESCHEDULED:
              return 'purple';
            case AppointmentStatus.LATE_CANCELLATION:
              return 'red';
            default:
              return 'gray';
          }
        },
      },

      // Get priority color for UI
      priorityColor: {
        needs: { priority: true },
        compute(appointment) {
          switch (appointment.priority) {
            case AppointmentPriority.EMERGENCY:
              return 'red';
            case AppointmentPriority.URGENT:
              return 'orange';
            case AppointmentPriority.ROUTINE:
              return 'blue';
            case AppointmentPriority.ELECTIVE:
              return 'gray';
            default:
              return 'gray';
          }
        },
      },

      // Check if appointment can be cancelled
      canBeCancelled: {
        needs: { status: true, appointmentDate: true },
        compute(appointment) {
          const now = new Date();
          const appointmentDate = new Date(appointment.appointmentDate);
          
          // Cannot cancel completed, already cancelled, or no-show appointments
          const nonCancellableStatuses = [
            AppointmentStatus.COMPLETED,
            AppointmentStatus.CANCELLED,
            AppointmentStatus.NO_SHOW,
            AppointmentStatus.LATE_CANCELLATION,
          ] as const;
          
          if (nonCancellableStatuses.includes(appointment.status as any)) {
            return false;
          }

          // Cannot cancel appointments that have already started
          if (appointment.status === AppointmentStatus.IN_PROGRESS) {
            return false;
          }

          // Can cancel future appointments
          return appointmentDate > now;
        },
      },

      // Check if appointment can be rescheduled
      canBeRescheduled: {
        needs: { status: true, appointmentDate: true },
        compute(appointment) {
          const now = new Date();
          const appointmentDate = new Date(appointment.appointmentDate);
          
          // Cannot reschedule completed, cancelled, or no-show appointments
          const nonReschedulableStatuses = [
            AppointmentStatus.COMPLETED,
            AppointmentStatus.CANCELLED,
            AppointmentStatus.NO_SHOW,
            AppointmentStatus.LATE_CANCELLATION,
          ] as const;
          
          if (nonReschedulableStatuses.includes(appointment.status as any)) {
            return false;
          }

          // Cannot reschedule appointments that have already started
          if (appointment.status === AppointmentStatus.IN_PROGRESS) {
            return false;
          }

          // Can reschedule future appointments or overdue appointments
          return true;
        },
      },

      // Check if cancellation is considered late
      isLateCancellation: {
        needs: { appointmentDate: true, cancellationDate: true },
        compute(appointment) {
          if (!appointment.cancellationDate) {
            return false;
          }

          const appointmentDate = new Date(appointment.appointmentDate);
          const cancellationDate = new Date(appointment.cancellationDate);
          
          // Late cancellation if cancelled less than 24 hours before appointment
          const hoursDifference = (appointmentDate.getTime() - cancellationDate.getTime()) / (1000 * 60 * 60);
          return hoursDifference < 24;
        },
      },

      // Get actual duration in minutes
      getActualDurationMinutes: {
        needs: { actualStartTime: true, actualEndTime: true },
        compute(appointment) {
          if (!appointment.actualStartTime || !appointment.actualEndTime) {
            return null;
          }

          const startTime = new Date(appointment.actualStartTime);
          const endTime = new Date(appointment.actualEndTime);
          
          return Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
        },
      },

      // Get wait time in minutes
      getWaitTimeMinutes: {
        needs: { appointmentDate: true, actualStartTime: true },
        compute(appointment) {
          if (!appointment.actualStartTime) {
            return null;
          }

          const scheduledTime = new Date(appointment.appointmentDate);
          const actualStartTime = new Date(appointment.actualStartTime);
          
          const waitTime = Math.round((actualStartTime.getTime() - scheduledTime.getTime()) / (1000 * 60));
          return Math.max(0, waitTime); // Don't return negative wait times
        },
      },

      // Format appointment time for display
      timeDisplay: {
        needs: { appointmentDate: true, durationMinutes: true },
        compute(appointment) {
          const startTime = new Date(appointment.appointmentDate);
          const endTime = new Date(startTime.getTime() + appointment.durationMinutes * 60000);
          
          const formatTime = (date: Date) => {
            return date.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true,
            });
          };

          return `${formatTime(startTime)} - ${formatTime(endTime)}`;
        },
      },

      // Get appointment type display name
      appointmentTypeDisplay: {
        needs: { appointmentType: true },
        compute(appointment) {
          switch (appointment.appointmentType) {
            case AppointmentType.CONSULTATION:
              return 'Consultation';
            case AppointmentType.ROUTINE_CHECKUP:
              return 'Routine Checkup';
            case AppointmentType.CLEANING:
              return 'Cleaning';
            case AppointmentType.EMERGENCY:
              return 'Emergency';
            case AppointmentType.TREATMENT:
              return 'Treatment';
            case AppointmentType.FOLLOW_UP:
              return 'Follow-up';
            case AppointmentType.SURGERY:
              return 'Surgery';
            case AppointmentType.ORTHODONTIC:
              return 'Orthodontic';
            case AppointmentType.RECALL:
              return 'Recall';
            case AppointmentType.NEW_PATIENT:
              return 'New Patient';
            case AppointmentType.TELEHEALTH:
              return 'Telehealth';
            case AppointmentType.BLOCK_TIME:
              return 'Block Time';
            default:
              return 'Unknown';
          }
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(appointment) {
          switch (appointment.status) {
            case AppointmentStatus.SCHEDULED:
              return 'Scheduled';
            case AppointmentStatus.CONFIRMED:
              return 'Confirmed';
            case AppointmentStatus.CHECKED_IN:
              return 'Checked In';
            case AppointmentStatus.IN_PROGRESS:
              return 'In Progress';
            case AppointmentStatus.COMPLETED:
              return 'Completed';
            case AppointmentStatus.NO_SHOW:
              return 'No Show';
            case AppointmentStatus.CANCELLED:
              return 'Cancelled';
            case AppointmentStatus.RESCHEDULED:
              return 'Rescheduled';
            case AppointmentStatus.LATE_CANCELLATION:
              return 'Late Cancellation';
            default:
              return 'Unknown';
          }
        },
      },

      // Get priority display name
      priorityDisplay: {
        needs: { priority: true },
        compute(appointment) {
          switch (appointment.priority) {
            case AppointmentPriority.EMERGENCY:
              return 'Emergency';
            case AppointmentPriority.URGENT:
              return 'Urgent';
            case AppointmentPriority.ROUTINE:
              return 'Routine';
            case AppointmentPriority.ELECTIVE:
              return 'Elective';
            default:
              return 'Unknown';
          }
        },
      },

      // Get next recall date for completed appointments
      getNextRecallDate: {
        needs: { 
          appointmentDate: true, 
          recallIntervalMonths: true, 
          status: true, 
          isRecallAppointment: true 
        },
        compute(appointment) {
          // Only calculate for completed appointments or existing recall appointments
          if (appointment.status !== AppointmentStatus.COMPLETED && !appointment.isRecallAppointment) {
            return null;
          }

          const intervalMonths = appointment.recallIntervalMonths || 6; // Default to 6 months
          const nextRecallDate = new Date(appointment.appointmentDate);
          nextRecallDate.setMonth(nextRecallDate.getMonth() + intervalMonths);
          
          return nextRecallDate;
        },
      },

      // Get recall type display name
      recallTypeDisplay: {
        needs: { recallType: true },
        compute(appointment) {
          if (!appointment.recallType) {
            return null;
          }

          switch (appointment.recallType) {
            case RecallType.ROUTINE_CLEANING:
              return 'Routine Cleaning';
            case RecallType.PERIODONTAL_MAINTENANCE:
              return 'Periodontal Maintenance';
            case RecallType.ORTHODONTIC_ADJUSTMENT:
              return 'Orthodontic Adjustment';
            case RecallType.POST_TREATMENT:
              return 'Post Treatment';
            case RecallType.FLUORIDE_TREATMENT:
              return 'Fluoride Treatment';
            case RecallType.ORAL_CANCER_SCREENING:
              return 'Oral Cancer Screening';
            case RecallType.CUSTOM:
              return 'Custom';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if appointment is a recall appointment that's overdue
      isRecallOverdue: {
        needs: { 
          isRecallAppointment: true, 
          appointmentDate: true, 
          status: true 
        },
        compute(appointment) {
          if (!appointment.isRecallAppointment) {
            return false;
          }

          const now = new Date();
          const appointmentDate = new Date(appointment.appointmentDate);
          
          return (
            appointmentDate < now &&
            (appointment.status === AppointmentStatus.SCHEDULED || 
             appointment.status === AppointmentStatus.CONFIRMED)
          );
        },
      },
    },
  },
});