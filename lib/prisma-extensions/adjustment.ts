import { Prisma, AdjustmentType, AdjustmentStatus } from "@prisma/client";

interface CreateAdjustmentInput {
  patientId: number;
  invoiceId?: number;
  adjustmentDate: Date;
  adjustmentType: AdjustmentType;
  amount: number;
  reason: string;
  notes?: string;
  createdById?: number;
  tenantId: string;
}

export const adjustmentExtension = Prisma.defineExtension({
  name: "adjustmentExtension",
  model: {
    adjustment: {
      // Generate unique adjustment number
      async generateAdjustmentNumber(tenantId: string): Promise<string> {
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        const dateStr = `${year}${month}${day}`;
        
        // Find the highest adjustment number for today
        const lastAdjustment = await this.findFirst({
          where: {
            tenantId,
            adjustmentNumber: {
              startsWith: `ADJ${dateStr}`,
            },
          },
          orderBy: {
            adjustmentNumber: 'desc',
          },
        });

        let sequence = 1;
        if (lastAdjustment) {
          const lastSequence = parseInt(lastAdjustment.adjustmentNumber.slice(-3));
          sequence = lastSequence + 1;
        }

        return `ADJ${dateStr}${sequence.toString().padStart(3, '0')}`;
      },

      // Create adjustment with auto-generated adjustment number
      async createAdjustment(data: CreateAdjustmentInput) {
        const adjustmentNumber = await this.generateAdjustmentNumber(data.tenantId);

        return this.create({
          data: {
            ...data,
            adjustmentNumber,
            status: AdjustmentStatus.PENDING,
          },
        });
      },

      // Approve adjustment
      async approveAdjustment(
        adjustmentId: number,
        approvedById: number,
        notes?: string
      ) {
        const adjustment = await this.findUnique({
          where: { id: adjustmentId },
          include: {
            invoice: true,
          },
        });

        if (!adjustment) {
          throw new Error('Adjustment not found');
        }

        if (adjustment.status !== AdjustmentStatus.PENDING) {
          throw new Error('Only pending adjustments can be approved');
        }

        // Update adjustment status
        const updatedAdjustment = await this.update({
          where: { id: adjustmentId },
          data: {
            status: AdjustmentStatus.APPROVED,
            approvedById,
            notes: notes || adjustment.notes,
          },
        });

        // If adjustment is linked to an invoice, update the invoice
        if (adjustment.invoice) {
          const adjustmentAmount = Number(adjustment.amount);
          const invoice = adjustment.invoice;
          
          // For credits (positive amounts), reduce the balance due
          // For charges (negative amounts), increase the balance due
          const newBalanceDue = Number(invoice.balanceDue) - adjustmentAmount;
          const newTotalAmount = Number(invoice.totalAmount) - adjustmentAmount;

          // Update invoice status if necessary
          let newStatus = invoice.status;
          if (newBalanceDue <= 0 && Number(invoice.amountPaid) > 0) {
            newStatus = 'PAID' as any;
          } else if (newBalanceDue > 0 && Number(invoice.amountPaid) > 0) {
            newStatus = 'PARTIAL_PAYMENT' as any;
          }

          await (this as any).invoice.update({
            where: { id: adjustment.invoiceId },
            data: {
              totalAmount: Math.max(0, newTotalAmount),
              balanceDue: Math.max(0, newBalanceDue),
              status: newStatus,
            },
          });
        }

        return updatedAdjustment;
      },

      // Deny adjustment
      async denyAdjustment(
        adjustmentId: number,
        approvedById: number,
        notes?: string
      ) {
        const adjustment = await this.findUnique({
          where: { id: adjustmentId },
        });

        if (!adjustment) {
          throw new Error('Adjustment not found');
        }

        if (adjustment.status !== AdjustmentStatus.PENDING) {
          throw new Error('Only pending adjustments can be denied');
        }

        return this.update({
          where: { id: adjustmentId },
          data: {
            status: AdjustmentStatus.DENIED,
            approvedById,
            notes: notes || adjustment.notes,
          },
        });
      },

      // Apply approved adjustment
      async applyAdjustment(adjustmentId: number, updatedById?: number) {
        const adjustment = await this.findUnique({
          where: { id: adjustmentId },
        });

        if (!adjustment) {
          throw new Error('Adjustment not found');
        }

        if (adjustment.status !== AdjustmentStatus.APPROVED) {
          throw new Error('Only approved adjustments can be applied');
        }

        return this.update({
          where: { id: adjustmentId },
          data: {
            status: AdjustmentStatus.APPLIED,
          },
        });
      },

      // Get adjustments by status
      async getAdjustmentsByStatus(tenantId: string, status: AdjustmentStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
            invoice: true,
            createdBy: true,
            approvedBy: true,
          },
          orderBy: {
            adjustmentDate: 'desc',
          },
        });
      },

      // Get pending adjustments requiring approval
      async getPendingAdjustments(tenantId: string) {
        return this.getAdjustmentsByStatus(tenantId, AdjustmentStatus.PENDING);
      },

      // Get adjustments for a patient
      async getAdjustmentsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            patientId,
          },
          include: {
            invoice: true,
            createdBy: true,
            approvedBy: true,
          },
          orderBy: {
            adjustmentDate: 'desc',
          },
        });
      },

      // Get adjustments for an invoice
      async getAdjustmentsForInvoice(tenantId: string, invoiceId: number) {
        return this.findMany({
          where: {
            tenantId,
            invoiceId,
          },
          include: {
            patient: true,
            createdBy: true,
            approvedBy: true,
          },
          orderBy: {
            adjustmentDate: 'desc',
          },
        });
      },

      // Get adjustments by type
      async getAdjustmentsByType(tenantId: string, adjustmentType: AdjustmentType) {
        return this.findMany({
          where: {
            tenantId,
            adjustmentType,
          },
          include: {
            patient: true,
            invoice: true,
            createdBy: true,
            approvedBy: true,
          },
          orderBy: {
            adjustmentDate: 'desc',
          },
        });
      },

      // Get adjustments by date range
      async getAdjustmentsByDateRange(
        tenantId: string,
        startDate: Date,
        endDate: Date,
        adjustmentType?: AdjustmentType
      ) {
        const where: any = {
          tenantId,
          adjustmentDate: {
            gte: startDate,
            lte: endDate,
          },
        };

        if (adjustmentType) {
          where.adjustmentType = adjustmentType;
        }

        return this.findMany({
          where,
          include: {
            patient: true,
            invoice: true,
            createdBy: true,
            approvedBy: true,
          },
          orderBy: {
            adjustmentDate: 'desc',
          },
        });
      },

      // Validate adjustment type and approval workflow
      async validateAdjustmentType(adjustmentType: AdjustmentType, amount: number, createdById?: number) {
        const errors: string[] = [];

        // Define which adjustment types require approval
        const requiresApproval = [
          AdjustmentType.WRITE_OFF,
          AdjustmentType.HARDSHIP_ADJUSTMENT,
          AdjustmentType.BILLING_ERROR,
        ];

        // Define amount thresholds that require approval
        const approvalThresholds = {
          [AdjustmentType.DISCOUNT]: 500,
          [AdjustmentType.COURTESY_ADJUSTMENT]: 200,
          [AdjustmentType.PROMPT_PAY_DISCOUNT]: 100,
          [AdjustmentType.FAMILY_DISCOUNT]: 300,
          [AdjustmentType.SENIOR_DISCOUNT]: 150,
        };

        // Check if adjustment type always requires approval
        const alwaysRequiresApproval = requiresApproval.includes(adjustmentType);

        // Check if amount exceeds threshold for this type
        const threshold = approvalThresholds[adjustmentType];
        const exceedsThreshold = threshold && Math.abs(amount) > threshold;

        // Validate amount is not zero
        if (amount === 0) {
          errors.push('Adjustment amount cannot be zero');
        }

        // Validate write-offs are positive (credits)
        if (adjustmentType === AdjustmentType.WRITE_OFF && amount < 0) {
          errors.push('Write-offs must be positive amounts (credits)');
        }

        // Validate billing error corrections can be positive or negative
        // (no specific validation needed)

        return {
          isValid: errors.length === 0,
          errors,
          requiresApproval: alwaysRequiresApproval || exceedsThreshold,
        };
      },

      // Get adjustment summary statistics
      async getAdjustmentSummary(tenantId: string, startDate?: Date, endDate?: Date) {
        const whereClause: any = { tenantId };
        
        if (startDate || endDate) {
          whereClause.adjustmentDate = {};
          if (startDate) whereClause.adjustmentDate.gte = startDate;
          if (endDate) whereClause.adjustmentDate.lte = endDate;
        }

        const adjustments = await this.findMany({
          where: whereClause,
        });

        const summary = {
          totalAdjustments: adjustments.length,
          totalCredits: 0,
          totalCharges: 0,
          netAdjustment: 0,
          typeBreakdown: {} as Record<AdjustmentType, { count: number; amount: number }>,
          statusBreakdown: {} as Record<AdjustmentStatus, { count: number; amount: number }>,
        };

        // Initialize breakdowns
        Object.values(AdjustmentType).forEach(type => {
          summary.typeBreakdown[type] = { count: 0, amount: 0 };
        });

        Object.values(AdjustmentStatus).forEach(status => {
          summary.statusBreakdown[status] = { count: 0, amount: 0 };
        });

        adjustments.forEach(adjustment => {
          const amount = Number(adjustment.amount);

          if (amount > 0) {
            summary.totalCredits += amount;
          } else {
            summary.totalCharges += Math.abs(amount);
          }

          summary.netAdjustment += amount;

          summary.typeBreakdown[adjustment.adjustmentType].count++;
          summary.typeBreakdown[adjustment.adjustmentType].amount += amount;

          summary.statusBreakdown[adjustment.status].count++;
          summary.statusBreakdown[adjustment.status].amount += amount;
        });

        return summary;
      },

      // Validate adjustment data
      async validateAdjustment(data: Partial<CreateAdjustmentInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.adjustmentDate) {
          errors.push('Adjustment date is required');
        }

        if (!data.adjustmentType) {
          errors.push('Adjustment type is required');
        }

        if (data.amount === undefined || data.amount === 0) {
          errors.push('Adjustment amount is required and cannot be zero');
        }

        if (!data.reason || data.reason.trim().length === 0) {
          errors.push('Reason is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate adjustment date is not too far in the future
        if (data.adjustmentDate) {
          const maxFutureDate = new Date();
          maxFutureDate.setDate(maxFutureDate.getDate() + 7);
          
          if (data.adjustmentDate > maxFutureDate) {
            errors.push('Adjustment date cannot be more than 7 days in the future');
          }
        }

        // Validate amount limits
        if (data.amount !== undefined) {
          if (Math.abs(data.amount) > 10000) {
            errors.push('Adjustment amount cannot exceed $10,000');
          }
        }

        // Validate reason length
        if (data.reason && data.reason.length > 500) {
          errors.push('Reason cannot exceed 500 characters');
        }

        // Validate notes length
        if (data.notes && data.notes.length > 1000) {
          errors.push('Notes cannot exceed 1000 characters');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },
    },
  },
  result: {
    adjustment: {
      // Check if adjustment is a credit (positive amount)
      isCredit: {
        needs: { amount: true },
        compute(adjustment) {
          return Number(adjustment.amount) > 0;
        },
      },

      // Check if adjustment is a charge (negative amount)
      isCharge: {
        needs: { amount: true },
        compute(adjustment) {
          return Number(adjustment.amount) < 0;
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(adjustment) {
          switch (adjustment.status) {
            case AdjustmentStatus.PENDING:
              return 'yellow';
            case AdjustmentStatus.APPROVED:
              return 'green';
            case AdjustmentStatus.DENIED:
              return 'red';
            case AdjustmentStatus.APPLIED:
              return 'blue';
            default:
              return 'gray';
          }
        },
      },

      // Get type color for UI
      typeColor: {
        needs: { adjustmentType: true },
        compute(adjustment) {
          switch (adjustment.adjustmentType) {
            case AdjustmentType.DISCOUNT:
              return 'blue';
            case AdjustmentType.WRITE_OFF:
              return 'red';
            case AdjustmentType.COURTESY_ADJUSTMENT:
              return 'green';
            case AdjustmentType.INSURANCE_ADJUSTMENT:
              return 'purple';
            case AdjustmentType.PROMPT_PAY_DISCOUNT:
              return 'blue';
            case AdjustmentType.FAMILY_DISCOUNT:
              return 'green';
            case AdjustmentType.SENIOR_DISCOUNT:
              return 'green';
            case AdjustmentType.HARDSHIP_ADJUSTMENT:
              return 'orange';
            case AdjustmentType.BILLING_ERROR:
              return 'red';
            case AdjustmentType.OTHER:
              return 'gray';
            default:
              return 'gray';
          }
        },
      },

      // Format adjustment number for display
      displayAdjustmentNumber: {
        needs: { adjustmentNumber: true },
        compute(adjustment) {
          return `#${adjustment.adjustmentNumber}`;
        },
      },

      // Format amount for display
      displayAmount: {
        needs: { amount: true },
        compute(adjustment) {
          const amount = Number(adjustment.amount);
          const sign = amount >= 0 ? '+' : '';
          return `${sign}$${amount.toFixed(2)}`;
        },
      },

      // Get adjustment type display name
      typeDisplayName: {
        needs: { adjustmentType: true },
        compute(adjustment) {
          return adjustment.adjustmentType.split('_').map(word => 
            word.charAt(0) + word.slice(1).toLowerCase()
          ).join(' ');
        },
      },

      // Check if adjustment can be approved
      canBeApproved: {
        needs: { status: true },
        compute(adjustment) {
          return adjustment.status === AdjustmentStatus.PENDING;
        },
      },

      // Check if adjustment can be applied
      canBeApplied: {
        needs: { status: true },
        compute(adjustment) {
          return adjustment.status === AdjustmentStatus.APPROVED;
        },
      },

      // Check if adjustment is final (cannot be modified)
      isFinal: {
        needs: { status: true },
        compute(adjustment) {
          return [AdjustmentStatus.APPLIED, AdjustmentStatus.DENIED].includes(adjustment.status);
        },
      },

      // Get absolute amount (always positive)
      absoluteAmount: {
        needs: { amount: true },
        compute(adjustment) {
          return Math.abs(Number(adjustment.amount));
        },
      },
    },
  },
});