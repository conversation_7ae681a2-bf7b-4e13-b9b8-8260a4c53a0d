import { Prisma, UserType } from "@prisma/client";
import bcrypt from "bcryptjs";

interface CreateUserInput {
  username?: string;
  phoneNumber?: string;
  email?: string;
  password: string;
  firstName?: string;
  lastName?: string;
  userType?: UserType;
  isStaff?: boolean;
  isActive?: boolean;
  isSuperuser?: boolean;
  tenantId: string;
  createdById?: number;
}

interface CreateSuperuserInput {
  username: string;
  password: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  tenantId: string;
  createdById?: number;
}

export const userExtension = Prisma.defineExtension({
  name: "userExtension",
  model: {
    user: {
      // Django CustomUserManager.create_user equivalent
      async createUser(data: CreateUserInput) {
        const { password, userType = UserType.PATIENT, ...userData } = data;

        // Validation: Either username or phone number must be provided
        if (!userData.username && !userData.phoneNumber) {
          throw new Error('Either username or phone number must be provided');
        }

        // For patients, use phone_number as username if username is not provided
        if (!userData.username && userData.phoneNumber && userType === UserType.PATIENT) {
          userData.username = userData.phoneNumber;
        }

        // Normalize email if provided
        if (userData.email) {
          userData.email = userData.email.toLowerCase().trim();
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 12);

        // Set staff status based on user type
        let isStaff = userData.isStaff;
        if (isStaff === undefined) {
          isStaff = userType === UserType.ADMIN || userType === UserType.RECEPTIONIST;
        }

        return this.create({
          data: {
            ...userData,
            password: hashedPassword,
            userType,
            isStaff,
            isActive: userData.isActive ?? true,
            isSuperuser: userData.isSuperuser ?? false,
          },
        });
      },

      // Django CustomUserManager.create_superuser equivalent
      async createSuperuser(data: CreateSuperuserInput) {
        const { password, ...userData } = data;

        if (!userData.username) {
          throw new Error('Username is required for superuser');
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 12);

        return this.create({
          data: {
            ...userData,
            password: hashedPassword,
            userType: UserType.ADMIN,
            isStaff: true,
            isSuperuser: true,
            isActive: true,
          },
        });
      },

      // Django AdminManager.get_queryset equivalent
      async getAdminUsers(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            userType: UserType.ADMIN,
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Django ReceptionistManager.get_queryset equivalent
      async getReceptionistUsers(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            userType: UserType.RECEPTIONIST,
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Django PatientUserManager.get_queryset equivalent
      async getPatientUsers(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            userType: UserType.PATIENT,
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Django DentistUserManager.get_queryset equivalent
      async getDentistUsers(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            userType: UserType.DENTIST,
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Get all staff users (Admin + Receptionist)
      async getStaffUsers(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            userType: {
              in: [UserType.ADMIN, UserType.RECEPTIONIST],
            },
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Get all active users
      async getActiveUsers(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            isActive: true,
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Create admin user with proper defaults
      async createAdminUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.username) {
          throw new Error('Username is required for Admin users');
        }

        return this.createUser({
          ...data,
          userType: UserType.ADMIN,
          isStaff: true,
        });
      },

      // Create receptionist user with proper defaults
      async createReceptionistUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.username) {
          throw new Error('Username is required for Receptionist users');
        }

        return this.createUser({
          ...data,
          userType: UserType.RECEPTIONIST,
          isStaff: true,
        });
      },

      // Create patient user with proper defaults
      async createPatientUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.phoneNumber) {
          throw new Error('Phone number is required for Patient users');
        }

        return this.createUser({
          ...data,
          userType: UserType.PATIENT,
          isStaff: false,
        });
      },

      // Create dentist user with proper defaults
      async createDentistUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.username && !data.phoneNumber) {
          throw new Error('Either username or phone number is required for Dentist users');
        }

        return this.createUser({
          ...data,
          userType: UserType.DENTIST,
          isStaff: false,
        });
      },

      // Django PhoneNumberBackend.authenticate equivalent for phone number authentication
      async authenticateByPhone(phoneNumber: string, password: string, tenantId: string) {
        try {
          const user = await this.findFirst({
            where: {
              tenantId,
              phoneNumber,
              userType: UserType.PATIENT,
              isActive: true,
            },
          });

          if (!user) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(password, user.password);
          if (!isPasswordValid) {
            return null;
          }

          // Update last login
          await this.update({
            where: { id: user.id },
            data: { lastLogin: new Date() },
          });

          return user;
        } catch (error) {
          return null;
        }
      },

      // Django PhoneNumberBackend.authenticate equivalent for username authentication
      async authenticateByUsername(username: string, password: string, tenantId: string) {
        try {
          const user = await this.findFirst({
            where: {
              tenantId,
              username,
              userType: {
                in: [UserType.ADMIN, UserType.RECEPTIONIST, UserType.DENTIST],
              },
              isActive: true,
            },
          });

          if (!user) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(password, user.password);
          if (!isPasswordValid) {
            return null;
          }

          // Update last login
          await this.update({
            where: { id: user.id },
            data: { lastLogin: new Date() },
          });

          return user;
        } catch (error) {
          return null;
        }
      },

      // Generic authenticate method that tries both phone and username
      async authenticate(identifier: string, password: string, tenantId: string) {
        // First try phone number authentication (for patients)
        const phoneUser = await this.authenticateByPhone(identifier, password, tenantId);
        if (phoneUser) {
          return phoneUser;
        }

        // Then try username authentication (for admin/receptionist/dentist)
        const usernameUser = await this.authenticateByUsername(identifier, password, tenantId);
        if (usernameUser) {
          return usernameUser;
        }

        return null;
      },

      // Validate password strength
      validatePassword(password: string): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (password.length < 8) {
          errors.push('Password must be at least 8 characters long');
        }

        if (!/[A-Z]/.test(password)) {
          errors.push('Password must contain at least one uppercase letter');
        }

        if (!/[a-z]/.test(password)) {
          errors.push('Password must contain at least one lowercase letter');
        }

        if (!/\d/.test(password)) {
          errors.push('Password must contain at least one number');
        }

        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
          errors.push('Password must contain at least one special character');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Change user password with validation
      async changePassword(userId: number, currentPassword: string, newPassword: string) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
          throw new Error('Current password is incorrect');
        }

        // Validate new password
        const validation = this.validatePassword(newPassword);
        if (!validation.isValid) {
          throw new Error(`Password validation failed: ${validation.errors.join(', ')}`);
        }

        // Hash new password
        const hashedNewPassword = await bcrypt.hash(newPassword, 12);

        // Update password
        return this.update({
          where: { id: userId },
          data: { password: hashedNewPassword },
        });
      },

      // Set staff status based on user type
      async setStaffStatus(userId: number, isStaff: boolean, updatedById?: number) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        // Validate staff status based on user type
        if (user.userType === UserType.PATIENT && isStaff) {
          throw new Error('Patient users cannot have staff status');
        }

        if ((user.userType === UserType.ADMIN || user.userType === UserType.RECEPTIONIST) && !isStaff) {
          throw new Error('Admin and Receptionist users must have staff status');
        }

        return this.update({
          where: { id: userId },
          data: {
            isStaff,
            updatedById,
          },
        });
      },

      // Set user type with proper validation and staff status adjustment
      async setUserType(userId: number, userType: UserType, updatedById?: number) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        // Validate required fields based on user type
        if (userType === UserType.PATIENT && !user.phoneNumber) {
          throw new Error('Phone number is required for Patient users');
        }

        if ((userType === UserType.ADMIN || userType === UserType.RECEPTIONIST) && !user.username) {
          throw new Error('Username is required for Admin and Receptionist users');
        }

        // Set appropriate staff status
        const isStaff = userType === UserType.ADMIN || userType === UserType.RECEPTIONIST;

        return this.update({
          where: { id: userId },
          data: {
            userType,
            isStaff,
            updatedById,
          },
        });
      },

      // Check if user has permission (basic permission checking)
      async hasPermission(userId: number, permission: string): Promise<boolean> {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user || !user.isActive) {
          return false;
        }

        // Superuser has all permissions
        if (user.isSuperuser) {
          return true;
        }

        // Basic permission logic based on user type
        switch (permission) {
          case 'admin':
            return user.userType === UserType.ADMIN;
          case 'staff':
            return user.isStaff;
          case 'patient_access':
            return user.userType === UserType.PATIENT;
          case 'clinical_access':
            return user.userType === UserType.DENTIST || user.userType === UserType.ADMIN;
          case 'reception_access':
            return user.userType === UserType.RECEPTIONIST || user.userType === UserType.ADMIN;
          default:
            return false;
        }
      },

      // Get user by phone number (for patient lookup)
      async findByPhoneNumber(phoneNumber: string, tenantId: string) {
        return this.findFirst({
          where: {
            tenantId,
            phoneNumber,
            isActive: true,
          },
        });
      },

      // Get user by username (for staff lookup)
      async findByUsername(username: string, tenantId: string) {
        return this.findFirst({
          where: {
            tenantId,
            username,
            isActive: true,
          },
        });
      },

      // Validation method equivalent to Django's clean()
      async validateUser(data: Partial<CreateUserInput>) {
        const errors: string[] = [];

        // Validate authentication requirements based on user type
        if (data.userType === UserType.ADMIN || data.userType === UserType.RECEPTIONIST) {
          if (!data.username) {
            errors.push('Username is required for Admin and Receptionist users');
          }
        } else if (data.userType === UserType.PATIENT) {
          if (!data.phoneNumber) {
            errors.push('Phone number is required for Patient users');
          }
        }

        // Validate email format if provided
        if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
          errors.push('Invalid email format');
        }

        // Validate phone number format if provided
        if (data.phoneNumber && !/^\+?1?\d{9,15}$/.test(data.phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
          errors.push('Invalid phone number format');
        }

        // Validate username format if provided
        if (data.username && !/^[\w.@+-]+$/.test(data.username)) {
          errors.push('Username may only contain letters, numbers, and @/./+/-/_ characters');
        }

        // Check for uniqueness within tenant
        if (data.tenantId) {
          if (data.username) {
            const existingUsername = await this.findFirst({
              where: {
                tenantId: data.tenantId,
                username: data.username,
              },
            });
            if (existingUsername) {
              errors.push('Username already exists in this tenant');
            }
          }

          if (data.phoneNumber) {
            const existingPhone = await this.findFirst({
              where: {
                tenantId: data.tenantId,
                phoneNumber: data.phoneNumber,
              },
            });
            if (existingPhone) {
              errors.push('Phone number already exists in this tenant');
            }
          }

          if (data.email) {
            const existingEmail = await this.findFirst({
              where: {
                tenantId: data.tenantId,
                email: data.email,
              },
            });
            if (existingEmail) {
              errors.push('Email already exists in this tenant');
            }
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Lifecycle method to set staff status based on user type (Django hook equivalent)
      async setStaffStatusByUserType(userId: number, updatedById?: number) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        const isStaff = user.userType === UserType.ADMIN || user.userType === UserType.RECEPTIONIST;

        return this.update({
          where: { id: userId },
          data: {
            isStaff,
            updatedById,
          },
        });
      },

      // Lifecycle method to set username for patient (Django hook equivalent)
      async setUsernameForPatient(userId: number, updatedById?: number) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        if (user.userType === UserType.PATIENT && !user.username && user.phoneNumber) {
          return this.update({
            where: { id: userId },
            data: {
              username: user.phoneNumber,
              updatedById,
            },
          });
        }

        return user;
      },

      // Clean method equivalent - comprehensive validation
      async cleanUser(userId: number) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        const validation = await this.validateUser({
          username: user.username || undefined,
          phoneNumber: user.phoneNumber || undefined,
          email: user.email || undefined,
          userType: user.userType,
          tenantId: user.tenantId,
        });

        if (!validation.isValid) {
          throw new Error(`User validation failed: ${validation.errors.join(', ')}`);
        }

        // Apply lifecycle hooks
        await this.setStaffStatusByUserType(userId);
        await this.setUsernameForPatient(userId);

        return this.findUnique({
          where: { id: userId },
        });
      },
    },
  },
  result: {
    user: {
      // Django User.get_full_name equivalent
      getFullName: {
        needs: { firstName: true, lastName: true, username: true, phoneNumber: true, email: true },
        compute(user) {
          const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
          return fullName || user.username || user.phoneNumber || user.email || 'Unknown User';
        },
      },

      // Django User.get_short_name equivalent
      getShortName: {
        needs: { firstName: true, username: true, phoneNumber: true },
        compute(user) {
          return user.firstName || user.username || user.phoneNumber || 'User';
        },
      },

      // Django User.__str__ equivalent
      displayName: {
        needs: { 
          firstName: true, 
          lastName: true, 
          username: true, 
          phoneNumber: true, 
          email: true, 
          userType: true 
        },
        compute(user) {
          const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
          const name = fullName || user.username || user.phoneNumber || user.email || 'Unknown User';
          
          if (user.userType === UserType.PATIENT && user.phoneNumber) {
            return `${name} (${user.phoneNumber})`;
          } else if (user.username) {
            return `${name} (${user.username})`;
          } else if (user.email) {
            return `${name} (${user.email})`;
          } else {
            return name;
          }
        },
      },

      // Check if user can authenticate (is_active equivalent)
      canAuthenticate: {
        needs: { isActive: true },
        compute(user) {
          return user.isActive;
        },
      },

      // Check if user is staff member
      isStaffMember: {
        needs: { isStaff: true, userType: true },
        compute(user) {
          return user.isStaff && (user.userType === UserType.ADMIN || user.userType === UserType.RECEPTIONIST);
        },
      },

      // Check if user is clinical provider
      isClinicalProvider: {
        needs: { userType: true },
        compute(user) {
          return user.userType === UserType.DENTIST || user.userType === UserType.ADMIN;
        },
      },

      // Get user type display name
      userTypeDisplay: {
        needs: { userType: true },
        compute(user) {
          switch (user.userType) {
            case UserType.ADMIN:
              return 'Admin';
            case UserType.RECEPTIONIST:
              return 'Receptionist';
            case UserType.PATIENT:
              return 'Patient';
            case UserType.DENTIST:
              return 'Dentist';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if user requires username for authentication
      requiresUsername: {
        needs: { userType: true },
        compute(user) {
          return user.userType === UserType.ADMIN || user.userType === UserType.RECEPTIONIST;
        },
      },

      // Check if user requires phone number for authentication
      requiresPhoneNumber: {
        needs: { userType: true },
        compute(user) {
          return user.userType === UserType.PATIENT;
        },
      },

      // Get primary authentication field
      primaryAuthField: {
        needs: { userType: true, username: true, phoneNumber: true },
        compute(user) {
          if (user.userType === UserType.PATIENT) {
            return user.phoneNumber;
          } else {
            return user.username;
          }
        },
      },

      // Check if user has complete profile
      hasCompleteProfile: {
        needs: { 
          firstName: true, 
          lastName: true, 
          email: true, 
          username: true, 
          phoneNumber: true, 
          userType: true 
        },
        compute(user) {
          const hasBasicInfo = user.firstName && user.lastName;
          
          if (user.userType === UserType.PATIENT) {
            return hasBasicInfo && user.phoneNumber;
          } else {
            return hasBasicInfo && user.username && user.email;
          }
        },
      },

      // Get initials
      initials: {
        needs: { firstName: true, lastName: true, username: true },
        compute(user) {
          if (user.firstName && user.lastName) {
            return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
          } else if (user.username) {
            return user.username.substring(0, 2).toUpperCase();
          } else {
            return 'U';
          }
        },
      },
    },
  },
});
