import { Prisma, InvoiceStatus } from "@prisma/client";

interface CreateInvoiceInput {
  patientId: number;
  invoiceDate: Date;
  subtotal?: number;
  totalAmount?: number;
  paymentTermsDays?: number;
  textToPaySent?: boolean;
  textToPayLink?: string;
  tenantId: string;
  createdById?: number;
}

export const invoiceExtension = Prisma.defineExtension({
  name: "invoiceExtension",
  model: {
    invoice: {
      // Generate unique invoice number
      async generateInvoiceNumber(tenantId: string): Promise<string> {
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        const dateStr = `${year}${month}${day}`;
        
        // Find the highest invoice number for today
        const lastInvoice = await this.findFirst({
          where: {
            tenantId,
            invoiceNumber: {
              startsWith: `INV${dateStr}`,
            },
          },
          orderBy: {
            invoiceNumber: 'desc',
          },
        });

        let sequence = 1;
        if (lastInvoice) {
          const lastSequence = parseInt(lastInvoice.invoiceNumber.slice(-3));
          sequence = lastSequence + 1;
        }

        return `INV${dateStr}${sequence.toString().padStart(3, '0')}`;
      },

      // Calculate due date from invoice date and payment terms
      calculateDueDate(invoiceDate: Date, paymentTermsDays: number = 30): Date {
        const dueDate = new Date(invoiceDate);
        dueDate.setDate(dueDate.getDate() + paymentTermsDays);
        return dueDate;
      },

      // Create invoice with auto-generated invoice number and calculated due date
      async createInvoice(data: CreateInvoiceInput) {
        const invoiceNumber = await this.generateInvoiceNumber(data.tenantId);
        const paymentTermsDays = data.paymentTermsDays || 30;
        const dueDate = this.calculateDueDate(data.invoiceDate, paymentTermsDays);
        const subtotal = data.subtotal || 0;
        const totalAmount = data.totalAmount || subtotal;

        return this.create({
          data: {
            ...data,
            invoiceNumber,
            dueDate,
            subtotal,
            totalAmount,
            balanceDue: totalAmount,
            paymentTermsDays,
            status: InvoiceStatus.DRAFT,
          },
        });
      },

      // Update invoice amounts and recalculate balance
      async updateInvoiceAmounts(
        invoiceId: number,
        subtotal: number,
        totalAmount?: number,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        const newTotalAmount = totalAmount || subtotal;
        const newBalanceDue = newTotalAmount - invoice.amountPaid;

        return this.update({
          where: { id: invoiceId },
          data: {
            subtotal,
            totalAmount: newTotalAmount,
            balanceDue: newBalanceDue,
            updatedById,
          },
        });
      },

      // Update amount paid and recalculate balance
      async updateAmountPaid(
        invoiceId: number,
        amountPaid: number,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        const newBalanceDue = invoice.totalAmount - amountPaid;
        let newStatus = invoice.status;

        // Update status based on payment
        if (amountPaid >= invoice.totalAmount) {
          newStatus = InvoiceStatus.PAID;
        } else if (amountPaid > 0) {
          newStatus = InvoiceStatus.PARTIAL_PAYMENT;
        }

        return this.update({
          where: { id: invoiceId },
          data: {
            amountPaid,
            balanceDue: newBalanceDue,
            status: newStatus,
            updatedById,
          },
        });
      },

      // Update invoice status with validation
      async updateInvoiceStatus(
        invoiceId: number,
        newStatus: InvoiceStatus,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        // Validate status transitions
        const validTransitions: Record<InvoiceStatus, InvoiceStatus[]> = {
          [InvoiceStatus.DRAFT]: [
            InvoiceStatus.PENDING,
            InvoiceStatus.CANCELLED,
          ],
          [InvoiceStatus.PENDING]: [
            InvoiceStatus.SENT,
            InvoiceStatus.CANCELLED,
          ],
          [InvoiceStatus.SENT]: [
            InvoiceStatus.VIEWED,
            InvoiceStatus.PARTIAL_PAYMENT,
            InvoiceStatus.PAID,
            InvoiceStatus.OVERDUE,
            InvoiceStatus.CANCELLED,
          ],
          [InvoiceStatus.VIEWED]: [
            InvoiceStatus.PARTIAL_PAYMENT,
            InvoiceStatus.PAID,
            InvoiceStatus.OVERDUE,
            InvoiceStatus.CANCELLED,
          ],
          [InvoiceStatus.PARTIAL_PAYMENT]: [
            InvoiceStatus.PAID,
            InvoiceStatus.OVERDUE,
            InvoiceStatus.CANCELLED,
          ],
          [InvoiceStatus.PAID]: [
            InvoiceStatus.REFUNDED,
          ],
          [InvoiceStatus.OVERDUE]: [
            InvoiceStatus.PARTIAL_PAYMENT,
            InvoiceStatus.PAID,
            InvoiceStatus.WRITE_OFF,
            InvoiceStatus.CANCELLED,
          ],
          [InvoiceStatus.CANCELLED]: [], // Final state
          [InvoiceStatus.REFUNDED]: [], // Final state
          [InvoiceStatus.WRITE_OFF]: [], // Final state
        };

        const allowedTransitions = validTransitions[invoice.status] || [];
        if (!allowedTransitions.includes(newStatus)) {
          throw new Error(`Cannot transition from ${invoice.status} to ${newStatus}`);
        }

        return this.update({
          where: { id: invoiceId },
          data: {
            status: newStatus,
            updatedById,
          },
        });
      },

      // Get invoices by status
      async getInvoicesByStatus(tenantId: string, status: InvoiceStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Get overdue invoices
      async getOverdueInvoices(tenantId: string) {
        const today = new Date();
        
        return this.findMany({
          where: {
            tenantId,
            dueDate: {
              lt: today,
            },
            status: {
              in: [
                InvoiceStatus.SENT,
                InvoiceStatus.VIEWED,
                InvoiceStatus.PARTIAL_PAYMENT,
                InvoiceStatus.OVERDUE,
              ],
            },
            balanceDue: {
              gt: 0,
            },
          },
          include: {
            patient: true,
          },
          orderBy: {
            dueDate: 'asc',
          },
        });
      },

      // Get invoices for a patient
      async getInvoicesForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            patientId,
          },
          include: {
            payments: true,
            paymentApplications: {
              include: {
                payment: true,
              },
            },
            adjustments: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Get invoices due within specified days
      async getInvoicesDueWithinDays(tenantId: string, days: number) {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + days);
        
        return this.findMany({
          where: {
            tenantId,
            dueDate: {
              lte: futureDate,
            },
            status: {
              in: [
                InvoiceStatus.SENT,
                InvoiceStatus.VIEWED,
                InvoiceStatus.PARTIAL_PAYMENT,
              ],
            },
            balanceDue: {
              gt: 0,
            },
          },
          include: {
            patient: true,
          },
          orderBy: {
            dueDate: 'asc',
          },
        });
      },

      // Mark invoice as sent and update text-to-pay
      async markInvoiceAsSent(
        invoiceId: number,
        textToPayLink?: string,
        updatedById?: number
      ) {
        const updateData: any = {
          status: InvoiceStatus.SENT,
          updatedById,
        };

        if (textToPayLink) {
          updateData.textToPaySent = true;
          updateData.textToPayLink = textToPayLink;
        }

        return this.update({
          where: { id: invoiceId },
          data: updateData,
        });
      },

      // Validate invoice data
      async validateInvoice(data: Partial<CreateInvoiceInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.invoiceDate) {
          errors.push('Invoice date is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate amounts
        if (data.subtotal !== undefined && data.subtotal < 0) {
          errors.push('Subtotal cannot be negative');
        }

        if (data.totalAmount !== undefined && data.totalAmount < 0) {
          errors.push('Total amount cannot be negative');
        }

        if (data.paymentTermsDays !== undefined && (data.paymentTermsDays < 0 || data.paymentTermsDays > 365)) {
          errors.push('Payment terms must be between 0 and 365 days');
        }

        // Validate invoice date is not too far in the future
        if (data.invoiceDate) {
          const maxFutureDate = new Date();
          maxFutureDate.setDate(maxFutureDate.getDate() + 30);
          
          if (data.invoiceDate > maxFutureDate) {
            errors.push('Invoice date cannot be more than 30 days in the future');
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Calculate aging buckets for accounts receivable
      async getAgingReport(tenantId: string) {
        const today = new Date();
        
        const invoices = await this.findMany({
          where: {
            tenantId,
            status: {
              in: [
                InvoiceStatus.SENT,
                InvoiceStatus.VIEWED,
                InvoiceStatus.PARTIAL_PAYMENT,
                InvoiceStatus.OVERDUE,
              ],
            },
            balanceDue: {
              gt: 0,
            },
          },
          include: {
            patient: true,
          },
        });

        const agingBuckets = {
          current: { count: 0, amount: 0 }, // 0-30 days
          thirtyDays: { count: 0, amount: 0 }, // 31-60 days
          sixtyDays: { count: 0, amount: 0 }, // 61-90 days
          ninetyDays: { count: 0, amount: 0 }, // 91-120 days
          overOneTwenty: { count: 0, amount: 0 }, // 120+ days
        };

        invoices.forEach(invoice => {
          const daysPastDue = Math.floor(
            (today.getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24)
          );
          const amount = Number(invoice.balanceDue);

          if (daysPastDue <= 30) {
            agingBuckets.current.count++;
            agingBuckets.current.amount += amount;
          } else if (daysPastDue <= 60) {
            agingBuckets.thirtyDays.count++;
            agingBuckets.thirtyDays.amount += amount;
          } else if (daysPastDue <= 90) {
            agingBuckets.sixtyDays.count++;
            agingBuckets.sixtyDays.amount += amount;
          } else if (daysPastDue <= 120) {
            agingBuckets.ninetyDays.count++;
            agingBuckets.ninetyDays.amount += amount;
          } else {
            agingBuckets.overOneTwenty.count++;
            agingBuckets.overOneTwenty.amount += amount;
          }
        });

        return agingBuckets;
      },

      // Get invoice summary statistics
      async getInvoiceSummary(tenantId: string, startDate?: Date, endDate?: Date) {
        const whereClause: any = { tenantId };
        
        if (startDate || endDate) {
          whereClause.invoiceDate = {};
          if (startDate) whereClause.invoiceDate.gte = startDate;
          if (endDate) whereClause.invoiceDate.lte = endDate;
        }

        const invoices = await this.findMany({
          where: whereClause,
        });

        const summary = {
          totalInvoices: invoices.length,
          totalAmount: 0,
          totalPaid: 0,
          totalOutstanding: 0,
          statusBreakdown: {} as Record<InvoiceStatus, { count: number; amount: number }>,
        };

        // Initialize status breakdown
        Object.values(InvoiceStatus).forEach(status => {
          summary.statusBreakdown[status] = { count: 0, amount: 0 };
        });

        invoices.forEach(invoice => {
          const totalAmount = Number(invoice.totalAmount);
          const amountPaid = Number(invoice.amountPaid);
          const balanceDue = Number(invoice.balanceDue);

          summary.totalAmount += totalAmount;
          summary.totalPaid += amountPaid;
          summary.totalOutstanding += balanceDue;

          summary.statusBreakdown[invoice.status].count++;
          summary.statusBreakdown[invoice.status].amount += totalAmount;
        });

        return summary;
      },
    },
  },
  result: {
    invoice: {
      // Check if invoice is overdue
      isOverdue: {
        needs: { dueDate: true, status: true, balanceDue: true },
        compute(invoice) {
          const today = new Date();
          const dueDate = new Date(invoice.dueDate);
          
          return (
            dueDate < today &&
            Number(invoice.balanceDue) > 0 &&
            [
              InvoiceStatus.SENT,
              InvoiceStatus.VIEWED,
              InvoiceStatus.PARTIAL_PAYMENT,
              InvoiceStatus.OVERDUE,
            ].includes(invoice.status)
          );
        },
      },

      // Calculate days past due
      daysPastDue: {
        needs: { dueDate: true },
        compute(invoice) {
          const today = new Date();
          const dueDate = new Date(invoice.dueDate);
          const diffTime = today.getTime() - dueDate.getTime();
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          
          return Math.max(0, diffDays);
        },
      },

      // Check if invoice is paid in full
      isPaidInFull: {
        needs: { balanceDue: true },
        compute(invoice) {
          return Number(invoice.balanceDue) <= 0;
        },
      },

      // Get payment percentage
      paymentPercentage: {
        needs: { totalAmount: true, amountPaid: true },
        compute(invoice) {
          const totalAmount = Number(invoice.totalAmount);
          const amountPaid = Number(invoice.amountPaid);
          
          if (totalAmount === 0) return 0;
          return Math.round((amountPaid / totalAmount) * 100);
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(invoice) {
          switch (invoice.status) {
            case InvoiceStatus.DRAFT:
              return 'gray';
            case InvoiceStatus.PENDING:
              return 'yellow';
            case InvoiceStatus.SENT:
              return 'blue';
            case InvoiceStatus.VIEWED:
              return 'blue';
            case InvoiceStatus.PARTIAL_PAYMENT:
              return 'orange';
            case InvoiceStatus.PAID:
              return 'green';
            case InvoiceStatus.OVERDUE:
              return 'red';
            case InvoiceStatus.CANCELLED:
              return 'gray';
            case InvoiceStatus.REFUNDED:
              return 'purple';
            case InvoiceStatus.WRITE_OFF:
              return 'red';
            default:
              return 'gray';
          }
        },
      },

      // Format invoice number for display
      displayInvoiceNumber: {
        needs: { invoiceNumber: true },
        compute(invoice) {
          return `#${invoice.invoiceNumber}`;
        },
      },

      // Check if invoice can be edited
      canBeEdited: {
        needs: { status: true },
        compute(invoice) {
          return [InvoiceStatus.DRAFT, InvoiceStatus.PENDING].includes(invoice.status);
        },
      },

      // Check if invoice can be cancelled
      canBeCancelled: {
        needs: { status: true, amountPaid: true },
        compute(invoice) {
          return (
            Number(invoice.amountPaid) === 0 &&
            ![
              InvoiceStatus.PAID,
              InvoiceStatus.CANCELLED,
              InvoiceStatus.REFUNDED,
              InvoiceStatus.WRITE_OFF,
            ].includes(invoice.status)
          );
        },
      },

      // Check if text-to-pay is available
      canSendTextToPay: {
        needs: { status: true },
        compute(invoice) {
          return [
            InvoiceStatus.SENT,
            InvoiceStatus.VIEWED,
            InvoiceStatus.PARTIAL_PAYMENT,
            InvoiceStatus.OVERDUE,
          ].includes(invoice.status);
        },
      },
    },
  },
});