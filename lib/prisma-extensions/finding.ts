import { Prisma, FindingCategory, FindingSeverity, FindingPrognosis, FindingStatus, TreatmentPriority } from "@prisma/client";

// Subcategory mappings for each finding category
const FINDING_SUBCATEGORIES: Record<FindingCategory, string[]> = {
  CARIES: [
    'Occlusal Caries',
    'Interproximal Caries',
    'Cervical Caries',
    'Root Caries',
    'Recurrent Caries',
    'Rampant Caries',
    'Early Childhood Caries'
  ],
  PERIODONTAL: [
    'Gingivitis',
    'Chronic Periodontitis',
    'Aggressive Periodontitis',
    'Necrotizing Gingivitis',
    'Gingival Recession',
    'Periodontal Abscess',
    'Furcation Involvement'
  ],
  ENDODONTIC: [
    'Pulpitis',
    'Pulp Necrosis',
    'Periapical Abscess',
    'Periapical Granuloma',
    'Root Resorption',
    'Calcific Metamorphosis',
    'Pulp Exposure'
  ],
  ORAL_PATHOLOGY: [
    'Oral Ulceration',
    'Leukoplakia',
    'Erythroplakia',
    'Oral Cancer',
    'Fibroma',
    '<PERSON><PERSON><PERSON>e',
    'Oral Lichen Planus'
  ],
  ORTHODONTIC: [
    'Crowding',
    'Spacing',
    'Overbite',
    'Underbite',
    'Crossbite',
    'Open Bite',
    'Midline Deviation'
  ],
  ORAL_SURGERY: [
    'Impacted Tooth',
    'Cyst',
    'Tumor',
    'Fracture',
    'Dislocation',
    'Extraction Site',
    'Dry Socket'
  ],
  PROSTHODONTIC: [
    'Missing Tooth',
    'Fractured Restoration',
    'Ill-fitting Prosthesis',
    'Crown Margin Defect',
    'Bridge Failure',
    'Denture Problems',
    'Implant Complications'
  ],
  PREVENTIVE: [
    'Plaque Accumulation',
    'Calculus Deposits',
    'Staining',
    'Fluorosis',
    'Erosion',
    'Attrition',
    'Abrasion'
  ],
  TMJ: [
    'TMJ Dysfunction',
    'Muscle Spasm',
    'Joint Clicking',
    'Limited Opening',
    'Bruxism',
    'Clenching',
    'Facial Pain'
  ],
  TRAUMA: [
    'Crown Fracture',
    'Root Fracture',
    'Luxation',
    'Avulsion',
    'Concussion',
    'Subluxation',
    'Alveolar Fracture'
  ],
  CONGENITAL: [
    'Supernumerary Tooth',
    'Congenitally Missing Tooth',
    'Enamel Defect',
    'Dentin Defect',
    'Cleft Palate',
    'Ankyloglossia',
    'Developmental Anomaly'
  ],
  OTHER: [
    'Unspecified Finding',
    'Multiple Findings',
    'Complex Case',
    'Referred Case',
    'Follow-up Required',
    'Additional Assessment Needed'
  ]
};

// Treatment suggestions based on finding category
const TREATMENT_SUGGESTIONS: Record<FindingCategory, string[]> = {
  CARIES: [
    'Composite Restoration',
    'Amalgam Restoration',
    'Crown',
    'Inlay/Onlay',
    'Root Canal Therapy',
    'Extraction'
  ],
  PERIODONTAL: [
    'Scaling and Root Planing',
    'Periodontal Surgery',
    'Gum Grafting',
    'Bone Grafting',
    'Guided Tissue Regeneration',
    'Maintenance Therapy'
  ],
  ENDODONTIC: [
    'Root Canal Therapy',
    'Apicoectomy',
    'Pulp Capping',
    'Pulpotomy',
    'Extraction',
    'Retreatment'
  ],
  ORAL_PATHOLOGY: [
    'Biopsy',
    'Excision',
    'Laser Therapy',
    'Medication',
    'Referral to Specialist',
    'Monitoring'
  ],
  ORTHODONTIC: [
    'Braces',
    'Clear Aligners',
    'Retainers',
    'Space Maintainers',
    'Functional Appliances',
    'Extraction'
  ],
  ORAL_SURGERY: [
    'Extraction',
    'Surgical Removal',
    'Bone Grafting',
    'Soft Tissue Surgery',
    'Implant Placement',
    'Cyst Enucleation'
  ],
  PROSTHODONTIC: [
    'Crown',
    'Bridge',
    'Partial Denture',
    'Complete Denture',
    'Implant',
    'Veneer'
  ],
  PREVENTIVE: [
    'Prophylaxis',
    'Fluoride Treatment',
    'Sealants',
    'Oral Hygiene Instruction',
    'Dietary Counseling',
    'Regular Maintenance'
  ],
  TMJ: [
    'Occlusal Adjustment',
    'Night Guard',
    'Physical Therapy',
    'Medication',
    'Stress Management',
    'Referral to Specialist'
  ],
  TRAUMA: [
    'Splinting',
    'Root Canal Therapy',
    'Crown',
    'Extraction',
    'Reimplantation',
    'Surgical Repair'
  ],
  CONGENITAL: [
    'Orthodontic Treatment',
    'Surgical Correction',
    'Prosthetic Replacement',
    'Monitoring',
    'Genetic Counseling',
    'Multidisciplinary Care'
  ],
  OTHER: [
    'Comprehensive Examination',
    'Additional Diagnostics',
    'Specialist Consultation',
    'Treatment Planning',
    'Monitoring',
    'Patient Education'
  ]
};

// Color codes for different severity levels and prognosis
const SEVERITY_COLORS: Record<FindingSeverity, string> = {
  MILD: '#28a745',      // Green
  MODERATE: '#ffc107',  // Yellow
  SEVERE: '#fd7e14',    // Orange
  EXTENSIVE: '#dc3545' // Red
};

const PROGNOSIS_COLORS: Record<FindingPrognosis, string> = {
  GOOD: '#28a745',      // Green
  FAIR: '#ffc107',      // Yellow
  POOR: '#fd7e14',      // Orange
  HOPELESS: '#dc3545'   // Red
};

export const findingExtension = Prisma.defineExtension({
  name: "findingExtension",
  model: {
    finding: {
      // Get subcategories for a specific finding category
      getSubcategoriesForCategory(category: FindingCategory): string[] {
        return FINDING_SUBCATEGORIES[category] || [];
      },

      // Get suggested treatments for a finding category
      getSuggestedTreatments(category: FindingCategory): string[] {
        return TREATMENT_SUGGESTIONS[category] || [];
      },

      // Update finding status
      async updateStatus(findingId: number, status: FindingStatus, updatedById?: number) {
        return this.update({
          where: { id: findingId },
          data: {
            status,
            updatedById,
          },
        });
      },

      // Update finding severity
      async updateSeverity(findingId: number, severity: FindingSeverity, updatedById?: number) {
        return this.update({
          where: { id: findingId },
          data: {
            severity,
            updatedById,
          },
        });
      },

      // Update finding prognosis
      async updatePrognosis(findingId: number, prognosis: FindingPrognosis, updatedById?: number) {
        return this.update({
          where: { id: findingId },
          data: {
            prognosis,
            updatedById,
          },
        });
      },

      // Mark finding as resolved
      async markResolved(findingId: number, updatedById?: number) {
        return this.updateStatus(findingId, FindingStatus.RESOLVED, updatedById);
      },

      // Mark finding as active
      async markActive(findingId: number, updatedById?: number) {
        return this.updateStatus(findingId, FindingStatus.ACTIVE, updatedById);
      },

      // Mark finding for monitoring
      async markForMonitoring(findingId: number, updatedById?: number) {
        return this.updateStatus(findingId, FindingStatus.MONITORING, updatedById);
      },

      // Get findings by category for a tenant
      async getFindingsByCategory(tenantId: string, category: FindingCategory) {
        return this.findMany({
          where: {
            tenantId,
            category,
          },
          include: {
            tooth: {
              include: {
                caseSheet: {
                  include: {
                    patient: true,
                  },
                },
              },
            },
            recordedBy: true,
            diagnoses: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Get findings by severity for a tenant
      async getFindingsBySeverity(tenantId: string, severity: FindingSeverity) {
        return this.findMany({
          where: {
            tenantId,
            severity,
          },
          include: {
            tooth: {
              include: {
                caseSheet: {
                  include: {
                    patient: true,
                  },
                },
              },
            },
            recordedBy: true,
            diagnoses: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Get urgent findings (severe or extensive severity)
      async getUrgentFindings(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            severity: {
              in: [FindingSeverity.SEVERE, FindingSeverity.EXTENSIVE],
            },
            status: FindingStatus.ACTIVE,
          },
          include: {
            tooth: {
              include: {
                caseSheet: {
                  include: {
                    patient: true,
                  },
                },
              },
            },
            recordedBy: true,
            diagnoses: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Get active findings for a patient
      async getActiveFindingsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            status: FindingStatus.ACTIVE,
            tooth: {
              caseSheet: {
                patientId,
              },
            },
          },
          include: {
            tooth: true,
            recordedBy: true,
            diagnoses: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Get findings for a specific tooth
      async getFindingsForTooth(tenantId: string, toothId: number) {
        return this.findMany({
          where: {
            tenantId,
            toothId,
          },
          include: {
            recordedBy: true,
            diagnoses: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Validate finding data
      async validateFinding(data: {
        category: FindingCategory;
        subcategory: string;
        severity: FindingSeverity;
        prognosis: FindingPrognosis;
        notes?: string;
      }) {
        const errors: string[] = [];

        // Validate subcategory belongs to category
        const validSubcategories = FINDING_SUBCATEGORIES[data.category];
        if (!validSubcategories.includes(data.subcategory)) {
          errors.push(`Invalid subcategory '${data.subcategory}' for category '${data.category}'`);
        }

        // Validate notes length
        if (data.notes && data.notes.length > 500) {
          errors.push('Notes cannot exceed 500 characters');
        }

        // Validate severity and prognosis combination
        if (data.severity === FindingSeverity.MILD && data.prognosis === FindingPrognosis.HOPELESS) {
          errors.push('Mild severity cannot have hopeless prognosis');
        }

        if (data.severity === FindingSeverity.EXTENSIVE && data.prognosis === FindingPrognosis.GOOD) {
          errors.push('Extensive severity typically should not have good prognosis');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },
    },
  },
  result: {
    finding: {
      // Check if finding is urgent (severe or extensive)
      isUrgent: {
        needs: { severity: true },
        compute(finding) {
          return finding.severity === FindingSeverity.SEVERE || 
                 finding.severity === FindingSeverity.EXTENSIVE;
        },
      },

      // Get priority level based on severity and prognosis
      priorityLevel: {
        needs: { severity: true, prognosis: true },
        compute(finding) {
          // Extensive severity is always urgent
          if (finding.severity === FindingSeverity.EXTENSIVE) {
            return TreatmentPriority.URGENT;
          }

          // Severe severity is high priority
          if (finding.severity === FindingSeverity.SEVERE) {
            return TreatmentPriority.HIGH;
          }

          // Poor or hopeless prognosis increases priority
          if (finding.prognosis === FindingPrognosis.HOPELESS) {
            return TreatmentPriority.URGENT;
          }

          if (finding.prognosis === FindingPrognosis.POOR) {
            return TreatmentPriority.HIGH;
          }

          // Moderate severity with fair prognosis is medium priority
          if (finding.severity === FindingSeverity.MODERATE) {
            return finding.prognosis === FindingPrognosis.FAIR ? 
              TreatmentPriority.HIGH : TreatmentPriority.MEDIUM;
          }

          // Mild severity is typically low priority
          return TreatmentPriority.LOW;
        },
      },

      // Get color code based on severity
      colorCode: {
        needs: { severity: true },
        compute(finding) {
          return SEVERITY_COLORS[finding.severity];
        },
      },

      // Get prognosis color code
      prognosisColorCode: {
        needs: { prognosis: true },
        compute(finding) {
          return PROGNOSIS_COLORS[finding.prognosis];
        },
      },

      // Get severity display name
      severityDisplay: {
        needs: { severity: true },
        compute(finding) {
          switch (finding.severity) {
            case FindingSeverity.MILD:
              return 'Mild';
            case FindingSeverity.MODERATE:
              return 'Moderate';
            case FindingSeverity.SEVERE:
              return 'Severe';
            case FindingSeverity.EXTENSIVE:
              return 'Extensive';
            default:
              return 'Unknown';
          }
        },
      },

      // Get prognosis display name
      prognosisDisplay: {
        needs: { prognosis: true },
        compute(finding) {
          switch (finding.prognosis) {
            case FindingPrognosis.GOOD:
              return 'Good';
            case FindingPrognosis.FAIR:
              return 'Fair';
            case FindingPrognosis.POOR:
              return 'Poor';
            case FindingPrognosis.HOPELESS:
              return 'Hopeless';
            default:
              return 'Unknown';
          }
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(finding) {
          switch (finding.status) {
            case FindingStatus.ACTIVE:
              return 'Active';
            case FindingStatus.RESOLVED:
              return 'Resolved';
            case FindingStatus.MONITORING:
              return 'Monitoring';
            default:
              return 'Unknown';
          }
        },
      },

      // Get category display name
      categoryDisplay: {
        needs: { category: true },
        compute(finding) {
          switch (finding.category) {
            case FindingCategory.CARIES:
              return 'Caries';
            case FindingCategory.PERIODONTAL:
              return 'Periodontal';
            case FindingCategory.ENDODONTIC:
              return 'Endodontic';
            case FindingCategory.ORAL_PATHOLOGY:
              return 'Oral Pathology';
            case FindingCategory.ORTHODONTIC:
              return 'Orthodontic';
            case FindingCategory.ORAL_SURGERY:
              return 'Oral Surgery';
            case FindingCategory.PROSTHODONTIC:
              return 'Prosthodontic';
            case FindingCategory.PREVENTIVE:
              return 'Preventive';
            case FindingCategory.TMJ:
              return 'TMJ';
            case FindingCategory.TRAUMA:
              return 'Trauma';
            case FindingCategory.CONGENITAL:
              return 'Congenital';
            case FindingCategory.OTHER:
              return 'Other';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if finding requires immediate attention
      requiresImmediateAttention: {
        needs: { severity: true, prognosis: true, category: true },
        compute(finding) {
          // Extensive severity always requires immediate attention
          if (finding.severity === FindingSeverity.EXTENSIVE) {
            return true;
          }

          // Hopeless prognosis requires immediate attention
          if (finding.prognosis === FindingPrognosis.HOPELESS) {
            return true;
          }

          // Severe trauma or oral pathology requires immediate attention
          if (finding.severity === FindingSeverity.SEVERE && 
              (finding.category === FindingCategory.TRAUMA || 
               finding.category === FindingCategory.ORAL_PATHOLOGY)) {
            return true;
          }

          return false;
        },
      },

      // Get suggested treatments for this finding
      suggestedTreatments: {
        needs: { category: true },
        compute(finding) {
          return TREATMENT_SUGGESTIONS[finding.category] || [];
        },
      },

      // Get available subcategories for this finding's category
      availableSubcategories: {
        needs: { category: true },
        compute(finding) {
          return FINDING_SUBCATEGORIES[finding.category] || [];
        },
      },

      // Check if finding is resolved
      isResolved: {
        needs: { status: true },
        compute(finding) {
          return finding.status === FindingStatus.RESOLVED;
        },
      },

      // Check if finding is being monitored
      isMonitoring: {
        needs: { status: true },
        compute(finding) {
          return finding.status === FindingStatus.MONITORING;
        },
      },

      // Check if finding is active
      isActive: {
        needs: { status: true },
        compute(finding) {
          return finding.status === FindingStatus.ACTIVE;
        },
      },
    },
  },
});