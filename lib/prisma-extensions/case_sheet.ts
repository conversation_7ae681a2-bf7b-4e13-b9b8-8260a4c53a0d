import { Prisma, CaseSheetStatus } from "@prisma/client";

interface CreateCaseSheetInput {
  patientId: number;
  clinicalNotes?: string;
  status?: CaseSheetStatus;
  lastVisitDate?: Date;
  tenantId: string;
  createdById?: number;
}

interface UpdateCaseSheetInput {
  clinicalNotes?: string;
  status?: CaseSheetStatus;
  lastVisitDate?: Date;
  updatedById?: number;
}

export const caseSheetExtension = Prisma.defineExtension({
  name: "caseSheetExtension",
  model: {
    caseSheet: {
      // Create case sheet with validation
      async createCaseSheet(data: CreateCaseSheetInput) {
        // Validate that patient exists and doesn't already have a case sheet
        const existingCaseSheet = await this.findFirst({
          where: {
            tenantId: data.tenantId,
            patientId: data.patientId,
          },
        });

        if (existingCaseSheet) {
          throw new Error('Patient already has a case sheet');
        }

        return this.create({
          data: {
            ...data,
            status: data.status ?? CaseSheetStatus.ACTIVE,
          },
        });
      },

      // Get case sheet by patient ID
      async findByPatientId(patientId: number, tenantId: string) {
        return this.findFirst({
          where: {
            tenantId,
            patientId,
          },
          include: {
            patient: true,
            teeth: {
              orderBy: {
                toothNumber: 'asc',
              },
            },
          },
        });
      },

      // Get all active case sheets
      async getActiveCaseSheets(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            status: CaseSheetStatus.ACTIVE,
          },
          include: {
            patient: true,
          },
          orderBy: [
            { lastVisitDate: 'desc' },
            { updatedAt: 'desc' },
          ],
        });
      },

      // Get all inactive case sheets
      async getInactiveCaseSheets(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            status: CaseSheetStatus.INACTIVE,
          },
          include: {
            patient: true,
          },
          orderBy: [
            { updatedAt: 'desc' },
          ],
        });
      },

      // Update case sheet status
      async updateStatus(caseSheetId: number, status: CaseSheetStatus, updatedById?: number) {
        const caseSheet = await this.findUnique({
          where: { id: caseSheetId },
        });

        if (!caseSheet) {
          throw new Error('Case sheet not found');
        }

        return this.update({
          where: { id: caseSheetId },
          data: {
            status,
            updatedById,
          },
        });
      },

      // Activate case sheet
      async activate(caseSheetId: number, updatedById?: number) {
        return this.updateStatus(caseSheetId, CaseSheetStatus.ACTIVE, updatedById);
      },

      // Deactivate case sheet
      async deactivate(caseSheetId: number, updatedById?: number) {
        return this.updateStatus(caseSheetId, CaseSheetStatus.INACTIVE, updatedById);
      },

      // Update clinical notes
      async updateClinicalNotes(caseSheetId: number, clinicalNotes: string, updatedById?: number) {
        const caseSheet = await this.findUnique({
          where: { id: caseSheetId },
        });

        if (!caseSheet) {
          throw new Error('Case sheet not found');
        }

        return this.update({
          where: { id: caseSheetId },
          data: {
            clinicalNotes,
            updatedById,
          },
        });
      },

      // Update last visit date
      async updateLastVisitDate(caseSheetId: number, lastVisitDate: Date, updatedById?: number) {
        const caseSheet = await this.findUnique({
          where: { id: caseSheetId },
        });

        if (!caseSheet) {
          throw new Error('Case sheet not found');
        }

        return this.update({
          where: { id: caseSheetId },
          data: {
            lastVisitDate,
            updatedById,
          },
        });
      },

      // Update last visit date to current date
      async markVisitToday(caseSheetId: number, updatedById?: number) {
        return this.updateLastVisitDate(caseSheetId, new Date(), updatedById);
      },

      // Get case sheets with recent visits (within specified days)
      async getRecentVisits(tenantId: string, withinDays: number = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - withinDays);

        return this.findMany({
          where: {
            tenantId,
            status: CaseSheetStatus.ACTIVE,
            lastVisitDate: {
              gte: cutoffDate,
            },
          },
          include: {
            patient: true,
          },
          orderBy: {
            lastVisitDate: 'desc',
          },
        });
      },

      // Get case sheets that haven't been visited recently (overdue for visit)
      async getOverdueVisits(tenantId: string, overdueAfterDays: number = 180) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - overdueAfterDays);

        return this.findMany({
          where: {
            tenantId,
            status: CaseSheetStatus.ACTIVE,
            OR: [
              {
                lastVisitDate: {
                  lt: cutoffDate,
                },
              },
              {
                lastVisitDate: null,
              },
            ],
          },
          include: {
            patient: true,
          },
          orderBy: [
            { lastVisitDate: 'asc' },
            { createdAt: 'asc' },
          ],
        });
      },

      // Link case sheet to patient (ensure proper relationship)
      async linkToPatient(caseSheetId: number, patientId: number, updatedById?: number) {
        // Validate that the patient exists and doesn't already have a case sheet
        const existingCaseSheet = await this.findFirst({
          where: {
            patientId,
            NOT: {
              id: caseSheetId,
            },
          },
        });

        if (existingCaseSheet) {
          throw new Error('Patient already has another case sheet');
        }

        return this.update({
          where: { id: caseSheetId },
          data: {
            patientId,
            updatedById,
          },
        });
      },

      // Get case sheet with full clinical data
      async getWithClinicalData(caseSheetId: number) {
        return this.findUnique({
          where: { id: caseSheetId },
          include: {
            patient: true,
            teeth: {
              include: {
                findings: {
                  include: {
                    diagnoses: true,
                    treatments: true,
                  },
                  orderBy: {
                    recordedDate: 'desc',
                  },
                },
              },
              orderBy: {
                toothNumber: 'asc',
              },
            },
          },
        });
      },

      // Search case sheets by patient name or clinic ID
      async searchByPatient(tenantId: string, searchTerm: string) {
        return this.findMany({
          where: {
            tenantId,
            patient: {
              OR: [
                {
                  firstName: {
                    contains: searchTerm,
                    mode: 'insensitive',
                  },
                },
                {
                  lastName: {
                    contains: searchTerm,
                    mode: 'insensitive',
                  },
                },
                {
                  clinicId: {
                    contains: searchTerm,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          },
          include: {
            patient: true,
          },
          orderBy: [
            { lastVisitDate: 'desc' },
            { updatedAt: 'desc' },
          ],
        });
      },

      // Validate case sheet data
      async validateCaseSheet(data: Partial<CreateCaseSheetInput | UpdateCaseSheetInput>) {
        const errors: string[] = [];

        // Validate clinical notes length if provided
        if (data.clinicalNotes && data.clinicalNotes.length > 2000) {
          errors.push('Clinical notes cannot exceed 2000 characters');
        }

        // Validate last visit date is not in the future
        if (data.lastVisitDate && data.lastVisitDate > new Date()) {
          errors.push('Last visit date cannot be in the future');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Clean method equivalent - comprehensive validation and updates
      async cleanCaseSheet(caseSheetId: number) {
        const caseSheet = await this.findUnique({
          where: { id: caseSheetId },
        });

        if (!caseSheet) {
          throw new Error('Case sheet not found');
        }

        const validation = await this.validateCaseSheet({
          clinicalNotes: caseSheet.clinicalNotes || undefined,
          lastVisitDate: caseSheet.lastVisitDate || undefined,
        });

        if (!validation.isValid) {
          throw new Error(`Case sheet validation failed: ${validation.errors.join(', ')}`);
        }

        return caseSheet;
      },
    },
  },
  result: {
    caseSheet: {
      // Check if case sheet is active
      isActive: {
        needs: { status: true },
        compute(caseSheet) {
          return caseSheet.status === CaseSheetStatus.ACTIVE;
        },
      },

      // Check if case sheet is inactive
      isInactive: {
        needs: { status: true },
        compute(caseSheet) {
          return caseSheet.status === CaseSheetStatus.INACTIVE;
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(caseSheet) {
          switch (caseSheet.status) {
            case CaseSheetStatus.ACTIVE:
              return 'Active';
            case CaseSheetStatus.INACTIVE:
              return 'Inactive';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if case sheet has clinical notes
      hasClinicalNotes: {
        needs: { clinicalNotes: true },
        compute(caseSheet) {
          return Boolean(caseSheet.clinicalNotes && caseSheet.clinicalNotes.trim().length > 0);
        },
      },

      // Get days since last visit
      daysSinceLastVisit: {
        needs: { lastVisitDate: true },
        compute(caseSheet) {
          if (!caseSheet.lastVisitDate) {
            return null;
          }
          const today = new Date();
          const lastVisit = new Date(caseSheet.lastVisitDate);
          const diffTime = Math.abs(today.getTime() - lastVisit.getTime());
          return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        },
      },

      // Check if case sheet is overdue for visit
      isOverdueForVisit: {
        needs: { lastVisitDate: true, status: true },
        compute(caseSheet) {
          if (caseSheet.status !== CaseSheetStatus.ACTIVE) {
            return false;
          }

          if (!caseSheet.lastVisitDate) {
            return true; // Never visited
          }

          const today = new Date();
          const lastVisit = new Date(caseSheet.lastVisitDate);
          const diffTime = today.getTime() - lastVisit.getTime();
          const daysSinceLastVisit = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          return daysSinceLastVisit > 180; // 6 months
        },
      },

      // Check if case sheet has recent visit
      hasRecentVisit: {
        needs: { lastVisitDate: true },
        compute(caseSheet) {
          if (!caseSheet.lastVisitDate) {
            return false;
          }

          const today = new Date();
          const lastVisit = new Date(caseSheet.lastVisitDate);
          const diffTime = today.getTime() - lastVisit.getTime();
          const daysSinceLastVisit = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          return daysSinceLastVisit <= 30; // Within 30 days
        },
      },

      // Get formatted last visit date
      lastVisitDateFormatted: {
        needs: { lastVisitDate: true },
        compute(caseSheet) {
          if (!caseSheet.lastVisitDate) {
            return 'Never';
          }
          return new Date(caseSheet.lastVisitDate).toLocaleDateString();
        },
      },

      // Get clinical notes preview (first 100 characters)
      clinicalNotesPreview: {
        needs: { clinicalNotes: true },
        compute(caseSheet) {
          if (!caseSheet.clinicalNotes) {
            return '';
          }
          return caseSheet.clinicalNotes.length > 100 
            ? caseSheet.clinicalNotes.substring(0, 100) + '...'
            : caseSheet.clinicalNotes;
        },
      },

      // Get clinical notes word count
      clinicalNotesWordCount: {
        needs: { clinicalNotes: true },
        compute(caseSheet) {
          if (!caseSheet.clinicalNotes) {
            return 0;
          }
          return caseSheet.clinicalNotes.trim().split(/\s+/).length;
        },
      },

      // Check if case sheet needs attention (no recent visit and active)
      needsAttention: {
        needs: { lastVisitDate: true, status: true },
        compute(caseSheet) {
          if (caseSheet.status !== CaseSheetStatus.ACTIVE) {
            return false;
          }

          if (!caseSheet.lastVisitDate) {
            return true; // Never visited
          }

          const today = new Date();
          const lastVisit = new Date(caseSheet.lastVisitDate);
          const diffTime = today.getTime() - lastVisit.getTime();
          const daysSinceLastVisit = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          return daysSinceLastVisit > 90; // 3 months
        },
      },
    },
  },
});