import { Prisma, DiagnosisCategory, DiagnosisSeverity, DiagnosisPrognosis, DiagnosisStatus, TreatmentPriority } from "@prisma/client";

// Color codes for different prognosis levels
const PROGNOSIS_COLORS: Record<DiagnosisPrognosis, string> = {
  GOOD: '#28a745',      // Green
  FAIR: '#ffc107',      // Yellow
  POOR: '#fd7e14',      // Orange
  HOPELESS: '#dc3545'   // Red
};

// Severity colors
const SEVERITY_COLORS: Record<DiagnosisSeverity, string> = {
  MILD: '#28a745',      // Green
  MODERATE: '#ffc107',  // Yellow
  SEVERE: '#fd7e14',    // Orange
  EXTENSIVE: '#dc3545' // Red
};

// Status colors
const STATUS_COLORS: Record<DiagnosisStatus, string> = {
  ACTIVE: '#dc3545',    // Red
  STABLE: '#ffc107',    // Yellow
  RESOLVED: '#28a745',  // Green
  CHRONIC: '#6f42c1',   // Purple
  RECURRENT: '#fd7e14'  // Orange
};

// Similar diagnosis mappings based on category
const SIMILAR_DIAGNOSES: Record<DiagnosisCategory, string[]> = {
  CARIES: [
    'Primary Caries',
    'Secondary Caries',
    'Root Surface Caries',
    'Arrested Caries',
    'Incipient Caries',
    'Cavitated Caries'
  ],
  PERIODONTAL: [
    'Gingivitis',
    'Chronic Periodontitis',
    'Aggressive Periodontitis',
    'Necrotizing Periodontal Disease',
    'Periodontal Abscess',
    'Peri-implantitis'
  ],
  ENDODONTIC: [
    'Reversible Pulpitis',
    'Irreversible Pulpitis',
    'Pulp Necrosis',
    'Acute Apical Periodontitis',
    'Chronic Apical Periodontitis',
    'Apical Abscess'
  ],
  ORAL_PATHOLOGY: [
    'Benign Lesion',
    'Malignant Lesion',
    'Premalignant Lesion',
    'Inflammatory Lesion',
    'Infectious Lesion',
    'Autoimmune Lesion'
  ],
  ORTHODONTIC: [
    'Class I Malocclusion',
    'Class II Malocclusion',
    'Class III Malocclusion',
    'Crowding',
    'Spacing',
    'Crossbite'
  ],
  ORAL_SURGERY: [
    'Impaction',
    'Cyst',
    'Tumor',
    'Fracture',
    'Dislocation',
    'Infection'
  ],
  PROSTHODONTIC: [
    'Partial Edentulism',
    'Complete Edentulism',
    'Crown Failure',
    'Bridge Failure',
    'Denture Problems',
    'Implant Failure'
  ],
  PREVENTIVE: [
    'High Caries Risk',
    'Moderate Caries Risk',
    'Low Caries Risk',
    'Periodontal Risk',
    'Oral Hygiene Issues',
    'Dietary Issues'
  ],
  TMJ: [
    'TMJ Disorder',
    'Muscle Disorder',
    'Joint Disorder',
    'Disc Displacement',
    'Arthritis',
    'Bruxism'
  ],
  TRAUMA: [
    'Crown Fracture',
    'Root Fracture',
    'Luxation',
    'Avulsion',
    'Concussion',
    'Alveolar Fracture'
  ],
  CONGENITAL: [
    'Developmental Anomaly',
    'Genetic Disorder',
    'Syndromic Condition',
    'Cleft Lip/Palate',
    'Tooth Anomaly',
    'Jaw Anomaly'
  ],
  OTHER: [
    'Unspecified Diagnosis',
    'Multiple Diagnoses',
    'Complex Case',
    'Differential Diagnosis',
    'Provisional Diagnosis',
    'Rule Out'
  ]
};

export const diagnosisExtension = Prisma.defineExtension({
  name: "diagnosisExtension",
  model: {
    diagnosis: {
      // Get similar diagnoses for a specific category
      getSimilarDiagnoses(category: DiagnosisCategory): string[] {
        return SIMILAR_DIAGNOSES[category] || [];
      },

      // Update diagnosis status
      async updateStatus(diagnosisId: number, status: DiagnosisStatus, updatedById?: number) {
        return this.update({
          where: { id: diagnosisId },
          data: {
            status,
            updatedById,
          },
        });
      },

      // Update diagnosis severity
      async updateSeverity(diagnosisId: number, severity: DiagnosisSeverity, updatedById?: number) {
        return this.update({
          where: { id: diagnosisId },
          data: {
            severity,
            updatedById,
          },
        });
      },

      // Update diagnosis prognosis
      async updatePrognosis(diagnosisId: number, prognosis: DiagnosisPrognosis, updatedById?: number) {
        return this.update({
          where: { id: diagnosisId },
          data: {
            prognosis,
            updatedById,
          },
        });
      },

      // Mark diagnosis as resolved
      async markResolved(diagnosisId: number, updatedById?: number) {
        return this.updateStatus(diagnosisId, DiagnosisStatus.RESOLVED, updatedById);
      },

      // Mark diagnosis as stable
      async markStable(diagnosisId: number, updatedById?: number) {
        return this.updateStatus(diagnosisId, DiagnosisStatus.STABLE, updatedById);
      },

      // Mark diagnosis as chronic
      async markChronic(diagnosisId: number, updatedById?: number) {
        return this.updateStatus(diagnosisId, DiagnosisStatus.CHRONIC, updatedById);
      },

      // Mark diagnosis as recurrent
      async markRecurrent(diagnosisId: number, updatedById?: number) {
        return this.updateStatus(diagnosisId, DiagnosisStatus.RECURRENT, updatedById);
      },

      // Get diagnoses by category for a tenant
      async getDiagnosesByCategory(tenantId: string, category: DiagnosisCategory) {
        return this.findMany({
          where: {
            tenantId,
            category,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get diagnoses by severity for a tenant
      async getDiagnosesBySeverity(tenantId: string, severity: DiagnosisSeverity) {
        return this.findMany({
          where: {
            tenantId,
            severity,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get urgent diagnoses (severe or extensive severity, or poor/hopeless prognosis)
      async getUrgentDiagnoses(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            OR: [
              {
                severity: {
                  in: [DiagnosisSeverity.SEVERE, DiagnosisSeverity.EXTENSIVE],
                },
              },
              {
                prognosis: {
                  in: [DiagnosisPrognosis.POOR, DiagnosisPrognosis.HOPELESS],
                },
              },
            ],
            status: {
              in: [DiagnosisStatus.ACTIVE, DiagnosisStatus.CHRONIC, DiagnosisStatus.RECURRENT],
            },
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get active diagnoses for a patient
      async getActiveDiagnosesForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            status: {
              in: [DiagnosisStatus.ACTIVE, DiagnosisStatus.CHRONIC, DiagnosisStatus.RECURRENT],
            },
            finding: {
              tooth: {
                caseSheet: {
                  patientId,
                },
              },
            },
          },
          include: {
            finding: {
              include: {
                tooth: true,
              },
            },
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get diagnoses for a specific finding
      async getDiagnosesForFinding(tenantId: string, findingId: number) {
        return this.findMany({
          where: {
            tenantId,
            findingId,
          },
          include: {
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get diagnoses for a specific tooth
      async getDiagnosesForTooth(tenantId: string, toothId: number) {
        return this.findMany({
          where: {
            tenantId,
            finding: {
              toothId,
            },
          },
          include: {
            finding: true,
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get periodontal diagnoses
      async getPeriodontalDiagnoses(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            category: DiagnosisCategory.PERIODONTAL,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            diagnosingProvider: true,
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Get diagnoses by provider
      async getDiagnosesByProvider(tenantId: string, providerId: number) {
        return this.findMany({
          where: {
            tenantId,
            diagnosingProviderId: providerId,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
          },
          orderBy: {
            diagnosisDate: 'desc',
          },
        });
      },

      // Validate diagnosis data
      async validateDiagnosis(data: {
        category: DiagnosisCategory;
        severity: DiagnosisSeverity;
        prognosis: DiagnosisPrognosis;
        status: DiagnosisStatus;
        notes?: string;
      }) {
        const errors: string[] = [];

        // Validate notes length
        if (data.notes && data.notes.length > 500) {
          errors.push('Notes cannot exceed 500 characters');
        }

        // Validate severity and prognosis combination
        if (data.severity === DiagnosisSeverity.MILD && data.prognosis === DiagnosisPrognosis.HOPELESS) {
          errors.push('Mild severity cannot have hopeless prognosis');
        }

        if (data.severity === DiagnosisSeverity.EXTENSIVE && data.prognosis === DiagnosisPrognosis.GOOD) {
          errors.push('Extensive severity typically should not have good prognosis');
        }

        // Validate status transitions
        if (data.status === DiagnosisStatus.RESOLVED && 
            (data.severity === DiagnosisSeverity.SEVERE || data.severity === DiagnosisSeverity.EXTENSIVE)) {
          // This might be valid, but worth noting
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Get diagnosis statistics for a tenant
      async getDiagnosisStatistics(tenantId: string) {
        const [
          totalDiagnoses,
          activeDiagnoses,
          resolvedDiagnoses,
          urgentDiagnoses,
          periodontalDiagnoses,
          cariesDiagnoses,
        ] = await Promise.all([
          this.count({ where: { tenantId } }),
          this.count({ 
            where: { 
              tenantId, 
              status: DiagnosisStatus.ACTIVE 
            } 
          }),
          this.count({ 
            where: { 
              tenantId, 
              status: DiagnosisStatus.RESOLVED 
            } 
          }),
          this.count({
            where: {
              tenantId,
              OR: [
                {
                  severity: {
                    in: [DiagnosisSeverity.SEVERE, DiagnosisSeverity.EXTENSIVE],
                  },
                },
                {
                  prognosis: {
                    in: [DiagnosisPrognosis.POOR, DiagnosisPrognosis.HOPELESS],
                  },
                },
              ],
              status: {
                in: [DiagnosisStatus.ACTIVE, DiagnosisStatus.CHRONIC, DiagnosisStatus.RECURRENT],
              },
            },
          }),
          this.count({ 
            where: { 
              tenantId, 
              category: DiagnosisCategory.PERIODONTAL 
            } 
          }),
          this.count({ 
            where: { 
              tenantId, 
              category: DiagnosisCategory.CARIES 
            } 
          }),
        ]);

        return {
          total: totalDiagnoses,
          active: activeDiagnoses,
          resolved: resolvedDiagnoses,
          urgent: urgentDiagnoses,
          periodontal: periodontalDiagnoses,
          caries: cariesDiagnoses,
        };
      },
    },
  },
  result: {
    diagnosis: {
      // Check if diagnosis is periodontal
      isPeriodontal: {
        needs: { category: true },
        compute(diagnosis) {
          return diagnosis.category === DiagnosisCategory.PERIODONTAL;
        },
      },

      // Check if diagnosis is urgent (severe/extensive severity or poor/hopeless prognosis)
      isUrgent: {
        needs: { severity: true, prognosis: true },
        compute(diagnosis) {
          return (
            diagnosis.severity === DiagnosisSeverity.SEVERE ||
            diagnosis.severity === DiagnosisSeverity.EXTENSIVE ||
            diagnosis.prognosis === DiagnosisPrognosis.POOR ||
            diagnosis.prognosis === DiagnosisPrognosis.HOPELESS
          );
        },
      },

      // Get priority level based on severity and prognosis
      priorityLevel: {
        needs: { severity: true, prognosis: true, status: true },
        compute(diagnosis) {
          // Resolved diagnoses have low priority
          if (diagnosis.status === DiagnosisStatus.RESOLVED) {
            return TreatmentPriority.LOW;
          }

          // Extensive severity is always urgent
          if (diagnosis.severity === DiagnosisSeverity.EXTENSIVE) {
            return TreatmentPriority.URGENT;
          }

          // Hopeless prognosis is urgent
          if (diagnosis.prognosis === DiagnosisPrognosis.HOPELESS) {
            return TreatmentPriority.URGENT;
          }

          // Severe severity is high priority
          if (diagnosis.severity === DiagnosisSeverity.SEVERE) {
            return TreatmentPriority.HIGH;
          }

          // Poor prognosis increases priority
          if (diagnosis.prognosis === DiagnosisPrognosis.POOR) {
            return TreatmentPriority.HIGH;
          }

          // Moderate severity with fair prognosis is medium priority
          if (diagnosis.severity === DiagnosisSeverity.MODERATE) {
            return diagnosis.prognosis === DiagnosisPrognosis.FAIR ? 
              TreatmentPriority.HIGH : TreatmentPriority.MEDIUM;
          }

          // Mild severity is typically low priority
          return TreatmentPriority.LOW;
        },
      },

      // Get prognosis color code
      prognosisColorCode: {
        needs: { prognosis: true },
        compute(diagnosis) {
          return PROGNOSIS_COLORS[diagnosis.prognosis];
        },
      },

      // Get severity color code
      severityColorCode: {
        needs: { severity: true },
        compute(diagnosis) {
          return SEVERITY_COLORS[diagnosis.severity];
        },
      },

      // Get status color code
      statusColorCode: {
        needs: { status: true },
        compute(diagnosis) {
          return STATUS_COLORS[diagnosis.status];
        },
      },

      // Get category display name
      categoryDisplay: {
        needs: { category: true },
        compute(diagnosis) {
          switch (diagnosis.category) {
            case DiagnosisCategory.CARIES:
              return 'Caries';
            case DiagnosisCategory.PERIODONTAL:
              return 'Periodontal';
            case DiagnosisCategory.ENDODONTIC:
              return 'Endodontic';
            case DiagnosisCategory.ORAL_PATHOLOGY:
              return 'Oral Pathology';
            case DiagnosisCategory.ORTHODONTIC:
              return 'Orthodontic';
            case DiagnosisCategory.ORAL_SURGERY:
              return 'Oral Surgery';
            case DiagnosisCategory.PROSTHODONTIC:
              return 'Prosthodontic';
            case DiagnosisCategory.PREVENTIVE:
              return 'Preventive';
            case DiagnosisCategory.TMJ:
              return 'TMJ';
            case DiagnosisCategory.TRAUMA:
              return 'Trauma';
            case DiagnosisCategory.CONGENITAL:
              return 'Congenital';
            case DiagnosisCategory.OTHER:
              return 'Other';
            default:
              return 'Unknown';
          }
        },
      },

      // Get severity display name
      severityDisplay: {
        needs: { severity: true },
        compute(diagnosis) {
          switch (diagnosis.severity) {
            case DiagnosisSeverity.MILD:
              return 'Mild';
            case DiagnosisSeverity.MODERATE:
              return 'Moderate';
            case DiagnosisSeverity.SEVERE:
              return 'Severe';
            case DiagnosisSeverity.EXTENSIVE:
              return 'Extensive';
            default:
              return 'Unknown';
          }
        },
      },

      // Get prognosis display name
      prognosisDisplay: {
        needs: { prognosis: true },
        compute(diagnosis) {
          switch (diagnosis.prognosis) {
            case DiagnosisPrognosis.GOOD:
              return 'Good';
            case DiagnosisPrognosis.FAIR:
              return 'Fair';
            case DiagnosisPrognosis.POOR:
              return 'Poor';
            case DiagnosisPrognosis.HOPELESS:
              return 'Hopeless';
            default:
              return 'Unknown';
          }
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(diagnosis) {
          switch (diagnosis.status) {
            case DiagnosisStatus.ACTIVE:
              return 'Active';
            case DiagnosisStatus.STABLE:
              return 'Stable';
            case DiagnosisStatus.RESOLVED:
              return 'Resolved';
            case DiagnosisStatus.CHRONIC:
              return 'Chronic';
            case DiagnosisStatus.RECURRENT:
              return 'Recurrent';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if diagnosis requires immediate attention
      requiresImmediateAttention: {
        needs: { severity: true, prognosis: true, category: true, status: true },
        compute(diagnosis) {
          // Resolved diagnoses don't require immediate attention
          if (diagnosis.status === DiagnosisStatus.RESOLVED) {
            return false;
          }

          // Extensive severity always requires immediate attention
          if (diagnosis.severity === DiagnosisSeverity.EXTENSIVE) {
            return true;
          }

          // Hopeless prognosis requires immediate attention
          if (diagnosis.prognosis === DiagnosisPrognosis.HOPELESS) {
            return true;
          }

          // Severe oral pathology or trauma requires immediate attention
          if (diagnosis.severity === DiagnosisSeverity.SEVERE && 
              (diagnosis.category === DiagnosisCategory.ORAL_PATHOLOGY || 
               diagnosis.category === DiagnosisCategory.TRAUMA)) {
            return true;
          }

          return false;
        },
      },

      // Get similar diagnoses for this diagnosis's category
      similarDiagnoses: {
        needs: { category: true },
        compute(diagnosis) {
          return SIMILAR_DIAGNOSES[diagnosis.category] || [];
        },
      },

      // Check if diagnosis is active
      isActive: {
        needs: { status: true },
        compute(diagnosis) {
          return diagnosis.status === DiagnosisStatus.ACTIVE;
        },
      },

      // Check if diagnosis is resolved
      isResolved: {
        needs: { status: true },
        compute(diagnosis) {
          return diagnosis.status === DiagnosisStatus.RESOLVED;
        },
      },

      // Check if diagnosis is stable
      isStable: {
        needs: { status: true },
        compute(diagnosis) {
          return diagnosis.status === DiagnosisStatus.STABLE;
        },
      },

      // Check if diagnosis is chronic
      isChronic: {
        needs: { status: true },
        compute(diagnosis) {
          return diagnosis.status === DiagnosisStatus.CHRONIC;
        },
      },

      // Check if diagnosis is recurrent
      isRecurrent: {
        needs: { status: true },
        compute(diagnosis) {
          return diagnosis.status === DiagnosisStatus.RECURRENT;
        },
      },

      // Get relationship methods for accessing patient and tooth through finding
      // Note: These would be accessed through the included finding relationship
      // For example: diagnosis.finding.tooth.caseSheet.patient
      // and: diagnosis.finding.tooth
    },
  },
});