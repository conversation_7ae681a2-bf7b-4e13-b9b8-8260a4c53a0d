import { Prisma, TreatmentStatus, TreatmentPriority, FindingCategory, FindingSeverity, FindingPrognosis } from "@prisma/client";

// Common procedures by finding category
const PROCEDURES_BY_CATEGORY: Record<FindingCategory, Array<{ code: string; name: string; averageCost: number }>> = {
  CARIES: [
    { code: 'D2140', name: 'Amalgam - one surface, primary or permanent', averageCost: 150 },
    { code: 'D2150', name: 'Amalgam - two surfaces, primary or permanent', averageCost: 180 },
    { code: 'D2160', name: 'Amalgam - three surfaces, primary or permanent', averageCost: 220 },
    { code: 'D2161', name: 'Amalgam - four or more surfaces, primary or permanent', averageCost: 260 },
    { code: 'D2330', name: 'Resin-based composite - one surface, anterior', averageCost: 180 },
    { code: 'D2331', name: 'Resin-based composite - two surfaces, anterior', averageCost: 220 },
    { code: 'D2332', name: 'Resin-based composite - three surfaces, anterior', averageCost: 260 },
    { code: 'D2335', name: 'Resin-based composite - four or more surfaces, anterior', averageCost: 300 },
    { code: 'D2391', name: 'Resin-based composite - one surface, posterior', averageCost: 200 },
    { code: 'D2392', name: 'Resin-based composite - two surfaces, posterior', averageCost: 240 },
    { code: 'D2393', name: 'Resin-based composite - three surfaces, posterior', averageCost: 280 },
    { code: 'D2394', name: 'Resin-based composite - four or more surfaces, posterior', averageCost: 320 },
  ],
  PERIODONTAL: [
    { code: 'D4341', name: 'Periodontal scaling and root planing - four or more teeth per quadrant', averageCost: 300 },
    { code: 'D4342', name: 'Periodontal scaling and root planing - one to three teeth per quadrant', averageCost: 200 },
    { code: 'D4910', name: 'Periodontal maintenance', averageCost: 150 },
    { code: 'D4920', name: 'Unscheduled dressing change', averageCost: 50 },
    { code: 'D4240', name: 'Gingival flap procedure, including root planing - four or more contiguous teeth', averageCost: 800 },
    { code: 'D4241', name: 'Gingival flap procedure, including root planing - one to three contiguous teeth', averageCost: 600 },
    { code: 'D4260', name: 'Osseous surgery (including elevation of a full thickness flap and closure) - four or more contiguous teeth', averageCost: 1200 },
    { code: 'D4261', name: 'Osseous surgery (including elevation of a full thickness flap and closure) - one to three contiguous teeth', averageCost: 900 },
  ],
  ENDODONTIC: [
    { code: 'D3310', name: 'Endodontic therapy, anterior tooth', averageCost: 800 },
    { code: 'D3320', name: 'Endodontic therapy, premolar tooth', averageCost: 900 },
    { code: 'D3330', name: 'Endodontic therapy, molar tooth', averageCost: 1200 },
    { code: 'D3346', name: 'Retreatment of previous root canal therapy - anterior', averageCost: 1000 },
    { code: 'D3347', name: 'Retreatment of previous root canal therapy - premolar', averageCost: 1100 },
    { code: 'D3348', name: 'Retreatment of previous root canal therapy - molar', averageCost: 1400 },
    { code: 'D3410', name: 'Apicoectomy - anterior', averageCost: 800 },
    { code: 'D3421', name: 'Apicoectomy - premolar (first root)', averageCost: 900 },
    { code: 'D3425', name: 'Apicoectomy - molar (first root)', averageCost: 1000 },
  ],
  ORAL_PATHOLOGY: [
    { code: 'D7286', name: 'Incisional biopsy of oral tissue - hard (bone, tooth)', averageCost: 400 },
    { code: 'D7285', name: 'Incisional biopsy of oral tissue - soft', averageCost: 300 },
    { code: 'D7288', name: 'Brush biopsy - transepithelial sample collection', averageCost: 200 },
    { code: 'D7140', name: 'Extraction, erupted tooth or exposed root', averageCost: 150 },
    { code: 'D7210', name: 'Extraction, erupted tooth requiring removal of bone and/or sectioning of tooth', averageCost: 300 },
  ],
  ORTHODONTIC: [
    { code: 'D8080', name: 'Comprehensive orthodontic treatment of the adolescent dentition', averageCost: 5000 },
    { code: 'D8090', name: 'Comprehensive orthodontic treatment of the adult dentition', averageCost: 6000 },
    { code: 'D8210', name: 'Removable appliance therapy', averageCost: 1500 },
    { code: 'D8220', name: 'Fixed appliance therapy', averageCost: 4000 },
    { code: 'D8680', name: 'Orthodontic retention (removal of appliances, construction and placement of retainer(s))', averageCost: 500 },
  ],
  ORAL_SURGERY: [
    { code: 'D7140', name: 'Extraction, erupted tooth or exposed root', averageCost: 150 },
    { code: 'D7210', name: 'Extraction, erupted tooth requiring removal of bone and/or sectioning of tooth', averageCost: 300 },
    { code: 'D7220', name: 'Removal of impacted tooth - soft tissue', averageCost: 400 },
    { code: 'D7230', name: 'Removal of impacted tooth - partially bony', averageCost: 500 },
    { code: 'D7240', name: 'Removal of impacted tooth - completely bony', averageCost: 600 },
    { code: 'D7250', name: 'Removal of residual tooth roots (cutting procedure)', averageCost: 200 },
    { code: 'D6010', name: 'Surgical placement of implant body: endosteal implant', averageCost: 2000 },
  ],
  PROSTHODONTIC: [
    { code: 'D2740', name: 'Crown - porcelain/ceramic substrate', averageCost: 1200 },
    { code: 'D2750', name: 'Crown - porcelain fused to high noble metal', averageCost: 1300 },
    { code: 'D2751', name: 'Crown - porcelain fused to predominantly base metal', averageCost: 1100 },
    { code: 'D2752', name: 'Crown - porcelain fused to noble metal', averageCost: 1250 },
    { code: 'D6240', name: 'Pontic - porcelain fused to high noble metal', averageCost: 1300 },
    { code: 'D6750', name: 'Crown - porcelain fused to high noble metal', averageCost: 1300 },
    { code: 'D5110', name: 'Complete denture - maxillary', averageCost: 1500 },
    { code: 'D5120', name: 'Complete denture - mandibular', averageCost: 1500 },
    { code: 'D5213', name: 'Partial denture - maxillary, cast metal framework with resin denture teeth', averageCost: 1800 },
    { code: 'D5214', name: 'Partial denture - mandibular, cast metal framework with resin denture teeth', averageCost: 1800 },
  ],
  PREVENTIVE: [
    { code: 'D1110', name: 'Prophylaxis - adult', averageCost: 100 },
    { code: 'D1120', name: 'Prophylaxis - child', averageCost: 80 },
    { code: 'D1206', name: 'Topical application of fluoride varnish', averageCost: 50 },
    { code: 'D1208', name: 'Topical application of fluoride - excluding varnish', averageCost: 40 },
    { code: 'D1351', name: 'Sealant - per tooth', averageCost: 60 },
    { code: 'D4355', name: 'Full mouth debridement to enable a comprehensive oral evaluation', averageCost: 150 },
  ],
  TMJ: [
    { code: 'D7880', name: 'Occlusal orthotic device, by report', averageCost: 600 },
    { code: 'D9940', name: 'Occlusal guard, by report', averageCost: 500 },
    { code: 'D9951', name: 'Occlusal adjustment - limited', averageCost: 200 },
    { code: 'D9952', name: 'Occlusal adjustment - complete', averageCost: 800 },
  ],
  TRAUMA: [
    { code: 'D7140', name: 'Extraction, erupted tooth or exposed root', averageCost: 150 },
    { code: 'D2740', name: 'Crown - porcelain/ceramic substrate', averageCost: 1200 },
    { code: 'D3310', name: 'Endodontic therapy, anterior tooth', averageCost: 800 },
    { code: 'D7110', name: 'Extraction, single tooth', averageCost: 150 },
    { code: 'D7270', name: 'Tooth reimplantation and/or stabilization of accidentally evulsed or displaced tooth', averageCost: 400 },
    { code: 'D7280', name: 'Exposure of an unerupted tooth', averageCost: 300 },
  ],
  CONGENITAL: [
    { code: 'D8080', name: 'Comprehensive orthodontic treatment of the adolescent dentition', averageCost: 5000 },
    { code: 'D6010', name: 'Surgical placement of implant body: endosteal implant', averageCost: 2000 },
    { code: 'D2740', name: 'Crown - porcelain/ceramic substrate', averageCost: 1200 },
    { code: 'D5110', name: 'Complete denture - maxillary', averageCost: 1500 },
    { code: 'D5120', name: 'Complete denture - mandibular', averageCost: 1500 },
  ],
  OTHER: [
    { code: 'D0150', name: 'Comprehensive oral evaluation - new or established patient', averageCost: 100 },
    { code: 'D0120', name: 'Periodic oral evaluation - established patient', averageCost: 80 },
    { code: 'D0210', name: 'Intraoral - periapical first radiographic image', averageCost: 40 },
    { code: 'D0330', name: 'Panoramic radiographic image', averageCost: 120 },
  ],
};

// Most commonly used procedures across all categories
const COMMON_PROCEDURES = [
  { code: 'D1110', name: 'Prophylaxis - adult', averageCost: 100 },
  { code: 'D0150', name: 'Comprehensive oral evaluation - new or established patient', averageCost: 100 },
  { code: 'D0120', name: 'Periodic oral evaluation - established patient', averageCost: 80 },
  { code: 'D2140', name: 'Amalgam - one surface, primary or permanent', averageCost: 150 },
  { code: 'D2391', name: 'Resin-based composite - one surface, posterior', averageCost: 200 },
  { code: 'D2330', name: 'Resin-based composite - one surface, anterior', averageCost: 180 },
  { code: 'D7140', name: 'Extraction, erupted tooth or exposed root', averageCost: 150 },
  { code: 'D2740', name: 'Crown - porcelain/ceramic substrate', averageCost: 1200 },
  { code: 'D3310', name: 'Endodontic therapy, anterior tooth', averageCost: 800 },
  { code: 'D4341', name: 'Periodontal scaling and root planing - four or more teeth per quadrant', averageCost: 300 },
  { code: 'D1206', name: 'Topical application of fluoride varnish', averageCost: 50 },
  { code: 'D0210', name: 'Intraoral - periapical first radiographic image', averageCost: 40 },
  { code: 'D0330', name: 'Panoramic radiographic image', averageCost: 120 },
  { code: 'D1351', name: 'Sealant - per tooth', averageCost: 60 },
  { code: 'D4910', name: 'Periodontal maintenance', averageCost: 150 },
];

export const treatmentExtension = Prisma.defineExtension({
  name: "treatmentExtension",
  model: {
    treatment: {
      // Mark treatment as completed with provider and date tracking
      async markCompleted(treatmentId: number, completedById: number, notes?: string) {
        const now = new Date();
        
        return this.update({
          where: { id: treatmentId },
          data: {
            status: TreatmentStatus.COMPLETED,
            completedDate: now,
            completedById,
            notes: notes || undefined,
            updatedAt: now,
            updatedById: completedById,
          },
        });
      },

      // Mark treatment as cancelled with reason logging
      async markCancelled(treatmentId: number, reason: string, cancelledById: number) {
        const now = new Date();
        
        return this.update({
          where: { id: treatmentId },
          data: {
            status: TreatmentStatus.CANCELLED,
            notes: reason,
            updatedAt: now,
            updatedById: cancelledById,
          },
        });
      },

      // Update treatment status
      async updateStatus(treatmentId: number, status: TreatmentStatus, updatedById?: number) {
        return this.update({
          where: { id: treatmentId },
          data: {
            status,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },

      // Update treatment priority
      async updatePriority(treatmentId: number, priority: TreatmentPriority, updatedById?: number) {
        return this.update({
          where: { id: treatmentId },
          data: {
            priority,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },

      // Assign treatment to a provider
      async assignToProvider(treatmentId: number, assignedToId: number, updatedById?: number) {
        return this.update({
          where: { id: treatmentId },
          data: {
            assignedToId,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },

      // Set planned date for treatment
      async setPlannedDate(treatmentId: number, plannedDate: Date, updatedById?: number) {
        return this.update({
          where: { id: treatmentId },
          data: {
            plannedDate,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },

      // Get treatments by status for a tenant
      async getTreatmentsByStatus(tenantId: string, status: TreatmentStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            assignedTo: true,
            completedBy: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      },

      // Get treatments by priority for a tenant
      async getTreatmentsByPriority(tenantId: string, priority: TreatmentPriority) {
        return this.findMany({
          where: {
            tenantId,
            priority,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            assignedTo: true,
            completedBy: true,
          },
          orderBy: {
            plannedDate: 'asc',
          },
        });
      },

      // Get overdue treatments for a tenant
      async getOverdueTreatments(tenantId: string) {
        const now = new Date();
        
        return this.findMany({
          where: {
            tenantId,
            status: {
              in: [TreatmentStatus.PLANNED, TreatmentStatus.IN_PROGRESS],
            },
            plannedDate: {
              lt: now,
            },
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            assignedTo: true,
          },
          orderBy: {
            plannedDate: 'asc',
          },
        });
      },

      // Get treatments for a specific patient
      async getTreatmentsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            finding: {
              tooth: {
                caseSheet: {
                  patientId,
                },
              },
            },
          },
          include: {
            finding: {
              include: {
                tooth: true,
              },
            },
            assignedTo: true,
            completedBy: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      },

      // Get treatments assigned to a specific provider
      async getTreatmentsForProvider(tenantId: string, providerId: number) {
        return this.findMany({
          where: {
            tenantId,
            assignedToId: providerId,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            completedBy: true,
          },
          orderBy: {
            plannedDate: 'asc',
          },
        });
      },

      // Get completed treatments by a specific provider
      async getCompletedTreatmentsByProvider(tenantId: string, providerId: number) {
        return this.findMany({
          where: {
            tenantId,
            completedById: providerId,
            status: TreatmentStatus.COMPLETED,
          },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
            assignedTo: true,
          },
          orderBy: {
            completedDate: 'desc',
          },
        });
      },

      // Get treatments with full patient and tooth information
      async getTreatmentWithDetails(treatmentId: number) {
        return this.findUnique({
          where: { id: treatmentId },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
                recordedBy: true,
                diagnoses: true,
              },
            },
            assignedTo: true,
            completedBy: true,
          },
        });
      },

      // Automatically set priority based on finding severity and prognosis
      async autoSetPriorityFromFinding(treatmentId: number, updatedById?: number) {
        // First get the treatment with its finding
        const treatment = await this.findUnique({
          where: { id: treatmentId },
          include: {
            finding: true,
          },
        });

        if (!treatment || !treatment.finding) {
          throw new Error('Treatment or associated finding not found');
        }

        let priority: TreatmentPriority;

        // Determine priority based on finding severity and prognosis
        if (treatment.finding.severity === FindingSeverity.EXTENSIVE) {
          priority = TreatmentPriority.URGENT;
        } else if (treatment.finding.severity === FindingSeverity.SEVERE) {
          priority = TreatmentPriority.HIGH;
        } else if (treatment.finding.prognosis === FindingPrognosis.HOPELESS) {
          priority = TreatmentPriority.URGENT;
        } else if (treatment.finding.prognosis === FindingPrognosis.POOR) {
          priority = TreatmentPriority.HIGH;
        } else if (treatment.finding.severity === FindingSeverity.MODERATE) {
          priority = treatment.finding.prognosis === FindingPrognosis.FAIR ? 
            TreatmentPriority.HIGH : TreatmentPriority.MEDIUM;
        } else {
          // Mild severity
          priority = TreatmentPriority.LOW;
        }

        // Update the treatment with the calculated priority
        return this.update({
          where: { id: treatmentId },
          data: {
            priority,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },

      // Get procedures for a specific finding category
      getProceduresForFindingCategory(category: FindingCategory) {
        return PROCEDURES_BY_CATEGORY[category] || [];
      },

      // Get common procedures across all categories
      getCommonProcedures() {
        return COMMON_PROCEDURES;
      },

      // Validate treatment cost and billing information
      async validateCostAndBilling(data: {
        cost: number;
        procedureCode: string;
        insurancePreAuth?: string;
        estimatedInsuranceCoverage?: number;
      }) {
        const errors: string[] = [];

        // Validate cost
        if (data.cost < 0) {
          errors.push('Cost cannot be negative');
        }

        if (data.cost > 999999.99) {
          errors.push('Cost cannot exceed $999,999.99');
        }

        // Validate cost is reasonable for procedure
        const procedureInfo = COMMON_PROCEDURES.find(p => p.code === data.procedureCode) ||
                             Object.values(PROCEDURES_BY_CATEGORY)
                               .flat()
                               .find(p => p.code === data.procedureCode);

        if (procedureInfo) {
          const averageCost = procedureInfo.averageCost;
          const minReasonable = averageCost * 0.3; // 30% of average
          const maxReasonable = averageCost * 3.0;  // 300% of average

          if (data.cost < minReasonable) {
            errors.push(`Cost seems unusually low for ${data.procedureCode}. Average cost is $${averageCost}`);
          }

          if (data.cost > maxReasonable) {
            errors.push(`Cost seems unusually high for ${data.procedureCode}. Average cost is $${averageCost}`);
          }
        }

        // Validate insurance coverage if provided
        if (data.estimatedInsuranceCoverage !== undefined) {
          if (data.estimatedInsuranceCoverage < 0) {
            errors.push('Insurance coverage cannot be negative');
          }

          if (data.estimatedInsuranceCoverage > data.cost) {
            errors.push('Insurance coverage cannot exceed treatment cost');
          }
        }

        // Validate pre-authorization format if provided
        if (data.insurancePreAuth && !/^[A-Z0-9]{6,20}$/.test(data.insurancePreAuth)) {
          errors.push('Insurance pre-authorization should be 6-20 alphanumeric characters');
        }

        return {
          isValid: errors.length === 0,
          errors,
          suggestedCost: procedureInfo?.averageCost,
        };
      },

      // Get cost estimate for a procedure code
      getCostEstimate(procedureCode: string) {
        const procedureInfo = COMMON_PROCEDURES.find(p => p.code === procedureCode) ||
                             Object.values(PROCEDURES_BY_CATEGORY)
                               .flat()
                               .find(p => p.code === procedureCode);

        return procedureInfo ? {
          code: procedureInfo.code,
          name: procedureInfo.name,
          averageCost: procedureInfo.averageCost,
          minCost: Math.round(procedureInfo.averageCost * 0.7),
          maxCost: Math.round(procedureInfo.averageCost * 1.5),
        } : null;
      },

      // Get patient through finding relationship
      async getPatient(treatmentId: number) {
        const treatment = await this.findUnique({
          where: { id: treatmentId },
          include: {
            finding: {
              include: {
                tooth: {
                  include: {
                    caseSheet: {
                      include: {
                        patient: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        return treatment?.finding?.tooth?.caseSheet?.patient || null;
      },

      // Get tooth through finding relationship
      async getTooth(treatmentId: number) {
        const treatment = await this.findUnique({
          where: { id: treatmentId },
          include: {
            finding: {
              include: {
                tooth: true,
              },
            },
          },
        });

        return treatment?.finding?.tooth || null;
      },

      // Validate treatment data
      async validateTreatment(data: {
        procedureCode: string;
        procedureName: string;
        cost: number;
        priority: TreatmentPriority;
        plannedDate?: Date;
        notes?: string;
      }) {
        const errors: string[] = [];

        // Validate procedure code format (assuming format like D1234)
        if (!/^[A-Z]\d{4}$/.test(data.procedureCode)) {
          errors.push('Procedure code must be in format like D1234');
        }

        // Validate procedure name
        if (!data.procedureName || data.procedureName.trim().length === 0) {
          errors.push('Procedure name is required');
        }

        if (data.procedureName && data.procedureName.length > 200) {
          errors.push('Procedure name cannot exceed 200 characters');
        }

        // Validate cost
        if (data.cost < 0) {
          errors.push('Cost cannot be negative');
        }

        if (data.cost > 999999.99) {
          errors.push('Cost cannot exceed $999,999.99');
        }

        // Validate planned date (cannot be in the past for new treatments)
        if (data.plannedDate && data.plannedDate < new Date()) {
          errors.push('Planned date cannot be in the past');
        }

        // Validate notes length
        if (data.notes && data.notes.length > 1000) {
          errors.push('Notes cannot exceed 1000 characters');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },
    },
  },
  result: {
    treatment: {
      // Check if treatment is completed
      isCompleted: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.COMPLETED;
        },
      },

      // Check if treatment is billable (completed treatments are billable)
      isBillable: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.COMPLETED;
        },
      },

      // Check if treatment is overdue
      isOverdue: {
        needs: { status: true, plannedDate: true },
        compute(treatment) {
          if (!treatment.plannedDate) return false;
          
          const now = new Date();
          return (treatment.status === TreatmentStatus.PLANNED || 
                  treatment.status === TreatmentStatus.IN_PROGRESS) && 
                 treatment.plannedDate < now;
        },
      },

      // Calculate days since treatment was planned
      daysSincePlanned: {
        needs: { createdAt: true },
        compute(treatment) {
          const now = new Date();
          const diffTime = Math.abs(now.getTime() - treatment.createdAt.getTime());
          return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(treatment) {
          switch (treatment.status) {
            case TreatmentStatus.PLANNED:
              return 'Planned';
            case TreatmentStatus.IN_PROGRESS:
              return 'In Progress';
            case TreatmentStatus.COMPLETED:
              return 'Completed';
            case TreatmentStatus.CANCELLED:
              return 'Cancelled';
            case TreatmentStatus.ON_HOLD:
              return 'On Hold';
            default:
              return 'Unknown';
          }
        },
      },

      // Get priority display name
      priorityDisplay: {
        needs: { priority: true },
        compute(treatment) {
          switch (treatment.priority) {
            case TreatmentPriority.LOW:
              return 'Low';
            case TreatmentPriority.MEDIUM:
              return 'Medium';
            case TreatmentPriority.HIGH:
              return 'High';
            case TreatmentPriority.URGENT:
              return 'Urgent';
            default:
              return 'Unknown';
          }
        },
      },

      // Get priority color code
      priorityColorCode: {
        needs: { priority: true },
        compute(treatment) {
          switch (treatment.priority) {
            case TreatmentPriority.LOW:
              return '#28a745';    // Green
            case TreatmentPriority.MEDIUM:
              return '#ffc107';    // Yellow
            case TreatmentPriority.HIGH:
              return '#fd7e14';    // Orange
            case TreatmentPriority.URGENT:
              return '#dc3545';    // Red
            default:
              return '#6c757d';    // Gray
          }
        },
      },

      // Get status color code
      statusColorCode: {
        needs: { status: true },
        compute(treatment) {
          switch (treatment.status) {
            case TreatmentStatus.PLANNED:
              return '#007bff';    // Blue
            case TreatmentStatus.IN_PROGRESS:
              return '#ffc107';    // Yellow
            case TreatmentStatus.COMPLETED:
              return '#28a745';    // Green
            case TreatmentStatus.CANCELLED:
              return '#dc3545';    // Red
            case TreatmentStatus.ON_HOLD:
              return '#6c757d';    // Gray
            default:
              return '#6c757d';    // Gray
          }
        },
      },

      // Check if treatment is in progress
      isInProgress: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.IN_PROGRESS;
        },
      },

      // Check if treatment is planned
      isPlanned: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.PLANNED;
        },
      },

      // Check if treatment is cancelled
      isCancelled: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.CANCELLED;
        },
      },

      // Check if treatment is on hold
      isOnHold: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.ON_HOLD;
        },
      },

      // Check if treatment is urgent priority
      isUrgent: {
        needs: { priority: true },
        compute(treatment) {
          return treatment.priority === TreatmentPriority.URGENT;
        },
      },

      // Check if treatment is high priority
      isHighPriority: {
        needs: { priority: true },
        compute(treatment) {
          return treatment.priority === TreatmentPriority.HIGH || 
                 treatment.priority === TreatmentPriority.URGENT;
        },
      },

      // Format cost as currency
      costFormatted: {
        needs: { cost: true },
        compute(treatment) {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
          }).format(Number(treatment.cost));
        },
      },

      // Get days until planned date (negative if overdue)
      daysUntilPlanned: {
        needs: { plannedDate: true },
        compute(treatment) {
          if (!treatment.plannedDate) return null;
          
          const now = new Date();
          const diffTime = treatment.plannedDate.getTime() - now.getTime();
          return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        },
      },

      // Check if treatment has been assigned to a provider
      isAssigned: {
        needs: { assignedToId: true },
        compute(treatment) {
          return treatment.assignedToId !== null;
        },
      },

      // Check if treatment needs assignment
      needsAssignment: {
        needs: { status: true, assignedToId: true },
        compute(treatment) {
          return (treatment.status === TreatmentStatus.PLANNED || 
                  treatment.status === TreatmentStatus.IN_PROGRESS) && 
                 treatment.assignedToId === null;
        },
      },



      // Check if procedure code is valid/recognized
      isRecognizedProcedure: {
        needs: { procedureCode: true },
        compute(treatment) {
          const allProcedures = [
            ...COMMON_PROCEDURES,
            ...Object.values(PROCEDURES_BY_CATEGORY).flat()
          ];
          return allProcedures.some(p => p.code === treatment.procedureCode);
        },
      },

      // Get average cost for this procedure code
      averageCostForProcedure: {
        needs: { procedureCode: true },
        compute(treatment) {
          const allProcedures = [
            ...COMMON_PROCEDURES,
            ...Object.values(PROCEDURES_BY_CATEGORY).flat()
          ];
          const procedureInfo = allProcedures.find(p => p.code === treatment.procedureCode);
          return procedureInfo?.averageCost || null;
        },
      },

      // Check if cost is within reasonable range for procedure
      isCostReasonable: {
        needs: { procedureCode: true, cost: true },
        compute(treatment) {
          const allProcedures = [
            ...COMMON_PROCEDURES,
            ...Object.values(PROCEDURES_BY_CATEGORY).flat()
          ];
          const procedureInfo = allProcedures.find(p => p.code === treatment.procedureCode);
          
          if (!procedureInfo) return true; // Can't validate unknown procedures
          
          const averageCost = procedureInfo.averageCost;
          const minReasonable = averageCost * 0.3;
          const maxReasonable = averageCost * 3.0;
          const cost = Number(treatment.cost);
          
          return cost >= minReasonable && cost <= maxReasonable;
        },
      },

      // Get cost variance from average (as percentage)
      costVariancePercentage: {
        needs: { procedureCode: true, cost: true },
        compute(treatment) {
          const allProcedures = [
            ...COMMON_PROCEDURES,
            ...Object.values(PROCEDURES_BY_CATEGORY).flat()
          ];
          const procedureInfo = allProcedures.find(p => p.code === treatment.procedureCode);
          
          if (!procedureInfo) return null;
          
          const averageCost = procedureInfo.averageCost;
          const cost = Number(treatment.cost);
          
          return Math.round(((cost - averageCost) / averageCost) * 100);
        },
      },

      // Check if treatment is ready for billing
      isReadyForBilling: {
        needs: { status: true, cost: true, procedureCode: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.COMPLETED &&
                 Number(treatment.cost) > 0 &&
                 treatment.procedureCode &&
                 treatment.procedureCode.trim().length > 0;
        },
      },

      // Get procedure category based on code
      procedureCategory: {
        needs: { procedureCode: true },
        compute(treatment) {
          for (const [category, procedures] of Object.entries(PROCEDURES_BY_CATEGORY)) {
            if (procedures.some(p => p.code === treatment.procedureCode)) {
              return category as FindingCategory;
            }
          }
          return null;
        },
      },
    },
  },
});